<template>
  <div class="enterprise-account-detail">
    <ContentWrap>
      <!-- 页面头部 -->
      <div class="page-header mb-20px">
        <ElButton @click="handleBack">
          <Icon icon="ep:arrow-left" class="mr-5px" />
          返回列表
        </ElButton>
        <div class="header-title">
          <h2>企业碳账户详情</h2>
          <ElTag :type="getStatusType(accountInfo.status)" size="large">
            {{ getStatusText(accountInfo.status) }}
          </ElTag>
        </div>
      </div>

      <!-- 基本信息 -->
      <ElCard class="mb-20px">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:office-building" class="mr-5px" />
            基本信息
          </div>
        </template>
        <ElRow :gutter="20">
          <ElCol :span="12">
            <div class="info-item">
              <label>企业名称：</label>
              <span>{{ accountInfo.enterpriseName }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>统一社会信用代码：</label>
              <span>{{ accountInfo.creditCode }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>法定代表人：</label>
              <span>{{ accountInfo.legalPerson }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>注册资本：</label>
              <span>{{ accountInfo.registeredCapital }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>成立日期：</label>
              <span>{{ accountInfo.establishmentDate }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>经营状态：</label>
              <span>{{ accountInfo.businessStatus }}</span>
            </div>
          </ElCol>
          <ElCol :span="24">
            <div class="info-item">
              <label>注册地址：</label>
              <span>{{ accountInfo.registeredAddress }}</span>
            </div>
          </ElCol>
          <ElCol :span="24">
            <div class="info-item">
              <label>经营范围：</label>
              <span>{{ accountInfo.businessScope }}</span>
            </div>
          </ElCol>
        </ElRow>
      </ElCard>

      <!-- 联系信息 -->
      <ElCard class="mb-20px">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:phone" class="mr-5px" />
            联系信息
          </div>
        </template>
        <ElRow :gutter="20">
          <ElCol :span="12">
            <div class="info-item">
              <label>联系人：</label>
              <span>{{ accountInfo.contactPerson }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>联系电话：</label>
              <span>{{ accountInfo.contactPhone }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>电子邮箱：</label>
              <span>{{ accountInfo.email }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>传真号码：</label>
              <span>{{ accountInfo.fax }}</span>
            </div>
          </ElCol>
        </ElRow>
      </ElCard>

      <!-- 账户信息 -->
      <ElCard class="mb-20px">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:user" class="mr-5px" />
            账户信息
          </div>
        </template>
        <ElRow :gutter="20">
          <ElCol :span="12">
            <div class="info-item">
              <label>账户编号：</label>
              <span>{{ accountInfo.accountNumber }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>注册时间：</label>
              <span>{{ accountInfo.registrationTime }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>最后登录时间：</label>
              <span>{{ accountInfo.lastLoginTime }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>账户状态：</label>
              <ElTag :type="getStatusType(accountInfo.status)">
                {{ getStatusText(accountInfo.status) }}
              </ElTag>
            </div>
          </ElCol>
        </ElRow>
      </ElCard>

      <!-- 操作记录 -->
      <ElCard>
        <template #header>
          <div class="card-header">
            <Icon icon="ep:document" class="mr-5px" />
            操作记录
          </div>
        </template>
        <ElTable :data="operationLogs" style="width: 100%">
          <ElTableColumn prop="operationType" label="操作类型" width="120" />
          <ElTableColumn prop="operationContent" label="操作内容" min-width="200" />
          <ElTableColumn prop="operator" label="操作人" width="120" />
          <ElTableColumn prop="operationTime" label="操作时间" width="180" />
          <ElTableColumn prop="remark" label="备注" min-width="150" />
        </ElTable>
      </ElCard>

      <!-- 操作按钮 -->
      <div class="action-buttons mt-20px">
        <ElButton type="primary" @click="handleEdit">
          <Icon icon="ep:edit" class="mr-5px" />
          编辑信息
        </ElButton>
        <ElButton type="warning" @click="handleFreeze" v-if="accountInfo.status === '1'">
          <Icon icon="ep:lock" class="mr-5px" />
          冻结账户
        </ElButton>
        <ElButton type="success" @click="handleUnfreeze" v-if="accountInfo.status === '2'">
          <Icon icon="ep:unlock" class="mr-5px" />
          解冻账户
        </ElButton>
        <ElButton type="danger" @click="handleCancel" v-if="accountInfo.status !== '3'">
          <Icon icon="ep:delete" class="mr-5px" />
          注销账户
        </ElButton>
      </div>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'

const router = useRouter()
const route = useRoute()

// 账户信息
const accountInfo = ref({
  id: '',
  enterpriseName: '',
  creditCode: '',
  legalPerson: '',
  registeredCapital: '',
  establishmentDate: '',
  businessStatus: '',
  registeredAddress: '',
  businessScope: '',
  contactPerson: '',
  contactPhone: '',
  email: '',
  fax: '',
  accountNumber: '',
  registrationTime: '',
  lastLoginTime: '',
  status: '1'
})

// 操作记录
const operationLogs = ref([
  {
    operationType: '账户创建',
    operationContent: '创建企业碳账户',
    operator: '系统管理员',
    operationTime: '2024-01-15 10:30:00',
    remark: '初始创建'
  },
  {
    operationType: '信息更新',
    operationContent: '更新联系方式',
    operator: '张三',
    operationTime: '2024-02-20 14:20:00',
    remark: '更新联系电话'
  }
])

// 状态相关方法
const getStatusType = (status: string) => {
  const statusMap = {
    '1': 'success',
    '2': 'warning',
    '3': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    '1': '正常',
    '2': '冻结',
    '3': '注销'
  }
  return statusMap[status] || '未知'
}

// 获取账户详情
const getAccountDetail = async () => {
  const id = route.params.id as string
  // 模拟API调用
  setTimeout(() => {
    accountInfo.value = {
      id: id,
      enterpriseName: '绿色科技有限公司',
      creditCode: '91110000********9X',
      legalPerson: '张三',
      registeredCapital: '1000万元',
      establishmentDate: '2020-05-15',
      businessStatus: '存续',
      registeredAddress: '北京市朝阳区科技园区创新大厦A座1001室',
      businessScope: '技术开发、技术咨询、技术服务；销售电子产品、计算机软硬件；环保技术推广服务。',
      contactPerson: '李经理',
      contactPhone: '***********',
      email: '<EMAIL>',
      fax: '010-********',
      accountNumber: 'CA2024010001',
      registrationTime: '2024-01-15 10:30:00',
      lastLoginTime: '2024-07-28 16:45:00',
      status: '1'
    }
  }, 500)
}

// 事件处理方法
const handleBack = () => {
  router.back()
}

const handleEdit = () => {
  ElMessage.info('编辑功能待实现')
}

const handleFreeze = async () => {
  try {
    await ElMessageBox.confirm('确定要冻结这个企业账户吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 模拟API调用
    accountInfo.value.status = '2'
    ElMessage.success('账户已冻结')
  } catch (error) {
    // 用户取消
  }
}

const handleUnfreeze = async () => {
  try {
    await ElMessageBox.confirm('确定要解冻这个企业账户吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 模拟API调用
    accountInfo.value.status = '1'
    ElMessage.success('账户已解冻')
  } catch (error) {
    // 用户取消
  }
}

const handleCancel = async () => {
  try {
    await ElMessageBox.confirm('确定要注销这个企业账户吗？注销后无法恢复！', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    })
    
    // 模拟API调用
    accountInfo.value.status = '3'
    ElMessage.success('账户已注销')
  } catch (error) {
    // 用户取消
  }
}

onMounted(() => {
  getAccountDetail()
})
</script>

<style lang="less" scoped>
.enterprise-account-detail {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .header-title {
      display: flex;
      align-items: center;
      gap: 15px;
      
      h2 {
        margin: 0;
        color: #303133;
      }
    }
  }
  
  .card-header {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #303133;
  }
  
  .info-item {
    margin-bottom: 15px;
    
    label {
      font-weight: 600;
      color: #606266;
      margin-right: 10px;
    }
    
    span {
      color: #303133;
    }
  }
  
  .action-buttons {
    text-align: center;
    
    .el-button {
      margin: 0 10px;
    }
  }
}
</style>
