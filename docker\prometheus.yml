# Prometheus 配置文件

global:
  scrape_interval: 15s
  evaluation_interval: 15s

rule_files:
  # - "first_rules.yml"
  # - "second_rules.yml"

scrape_configs:
  # Prometheus 自身监控
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']

  # 前端应用监控
  - job_name: 'carbon-frontend'
    static_configs:
      - targets: ['carbon-account-frontend:80']
    metrics_path: '/metrics'
    scrape_interval: 30s

  # 后端应用监控
  - job_name: 'carbon-backend'
    static_configs:
      - targets: ['carbon-account-backend:3000']
    metrics_path: '/metrics'
    scrape_interval: 15s

  # PostgreSQL 监控（需要 postgres_exporter）
  - job_name: 'postgres'
    static_configs:
      - targets: ['postgres-exporter:9187']
    scrape_interval: 30s

  # Redis 监控（需要 redis_exporter）
  - job_name: 'redis'
    static_configs:
      - targets: ['redis-exporter:9121']
    scrape_interval: 30s

  # Nginx 监控（需要 nginx-prometheus-exporter）
  - job_name: 'nginx'
    static_configs:
      - targets: ['nginx-exporter:9113']
    scrape_interval: 30s

  # Node Exporter 系统监控
  - job_name: 'node'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s

alerting:
  alertmanagers:
    - static_configs:
        - targets:
          # - alertmanager:9093
