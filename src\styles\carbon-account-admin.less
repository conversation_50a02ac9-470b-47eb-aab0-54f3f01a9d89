// 碳账户管理后台通用样式

// 颜色变量
:root {
  // 主题色
  --carbon-primary: #409eff;
  --carbon-success: #67c23a;
  --carbon-warning: #e6a23c;
  --carbon-danger: #f56c6c;
  --carbon-info: #909399;
  
  // 碳相关主题色
  --carbon-green: #52c41a;
  --carbon-blue: #1890ff;
  --carbon-orange: #fa8c16;
  --carbon-red: #ff4d4f;
  --carbon-purple: #722ed1;
  
  // 背景色
  --carbon-bg-primary: #ffffff;
  --carbon-bg-secondary: #f8f9fa;
  --carbon-bg-tertiary: #f0f2f5;
  
  // 文字色
  --carbon-text-primary: #303133;
  --carbon-text-regular: #606266;
  --carbon-text-secondary: #909399;
  --carbon-text-placeholder: #c0c4cc;
  
  // 边框色
  --carbon-border-light: #ebeef5;
  --carbon-border-base: #dcdfe6;
  --carbon-border-dark: #d4d7de;
  
  // 阴影
  --carbon-shadow-light: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --carbon-shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --carbon-shadow-dark: 0 4px 8px rgba(0, 0, 0, 0.12), 0 0 12px rgba(0, 0, 0, 0.04);
}

// 通用布局样式
.carbon-admin-layout {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
    padding: 16px 0;
    
    .header-title {
      display: flex;
      align-items: center;
      gap: 15px;
      
      h1, h2, h3 {
        margin: 0;
        color: var(--carbon-text-primary);
        font-weight: 600;
      }
      
      h1 { font-size: 24px; }
      h2 { font-size: 20px; }
      h3 { font-size: 18px; }
    }
    
    .header-actions {
      display: flex;
      gap: 12px;
    }
  }
  
  .page-content {
    .content-section {
      margin-bottom: 20px;
      
      &:last-child {
        margin-bottom: 0;
      }
    }
  }
}

// 卡片样式增强
.carbon-card {
  border-radius: 8px;
  box-shadow: var(--carbon-shadow-light);
  transition: all 0.3s ease;
  
  &:hover {
    box-shadow: var(--carbon-shadow-base);
  }
  
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 600;
    color: var(--carbon-text-primary);
    
    .header-icon {
      margin-right: 8px;
      color: var(--carbon-primary);
    }
    
    .header-actions {
      display: flex;
      gap: 8px;
    }
  }
}

// 统计卡片样式
.stats-card {
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 8px;
  overflow: hidden;
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--carbon-shadow-dark);
  }
  
  &.total {
    border-left: 4px solid var(--carbon-blue);
    
    .stats-icon {
      color: var(--carbon-blue);
    }
  }
  
  &.active {
    border-left: 4px solid var(--carbon-green);
    
    .stats-icon {
      color: var(--carbon-green);
    }
  }
  
  &.carbon {
    border-left: 4px solid var(--carbon-orange);
    
    .stats-icon {
      color: var(--carbon-orange);
    }
  }
  
  &.emission {
    border-left: 4px solid var(--carbon-red);
    
    .stats-icon {
      color: var(--carbon-red);
    }
  }
  
  &.score {
    border-left: 4px solid var(--carbon-purple);
    
    .stats-icon {
      color: var(--carbon-purple);
    }
  }
  
  .stats-content {
    display: flex;
    align-items: center;
    gap: 16px;
    padding: 20px;
    
    .stats-icon {
      font-size: 32px;
    }
    
    .stats-info {
      flex: 1;
      
      .stats-number {
        font-size: 28px;
        font-weight: bold;
        color: var(--carbon-text-primary);
        margin-bottom: 4px;
        line-height: 1;
      }
      
      .stats-label {
        font-size: 14px;
        color: var(--carbon-text-secondary);
        line-height: 1;
      }
    }
  }
}

// 搜索区域样式
.search-area {
  background: var(--carbon-bg-secondary);
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 20px;
  
  .el-form-item {
    margin-bottom: 16px;
  }
  
  .search-actions {
    text-align: center;
    margin-top: 16px;
    
    .el-button {
      margin: 0 8px;
    }
  }
}

// 操作按钮区域
.action-area {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  
  .action-left {
    display: flex;
    gap: 12px;
  }
  
  .action-right {
    display: flex;
    gap: 8px;
  }
}

// 信息展示项
.info-item {
  margin-bottom: 16px;
  display: flex;
  align-items: flex-start;
  
  label {
    font-weight: 600;
    color: var(--carbon-text-regular);
    margin-right: 12px;
    min-width: 100px;
    flex-shrink: 0;
  }
  
  span {
    color: var(--carbon-text-primary);
    flex: 1;
    word-break: break-all;
  }
  
  &.vertical {
    flex-direction: column;
    
    label {
      margin-right: 0;
      margin-bottom: 8px;
    }
  }
}

// 状态标签样式
.status-tag {
  &.status-normal {
    background-color: #f0f9ff;
    color: var(--carbon-success);
    border-color: var(--carbon-success);
  }
  
  &.status-frozen {
    background-color: #fff7e6;
    color: var(--carbon-warning);
    border-color: var(--carbon-warning);
  }
  
  &.status-cancelled {
    background-color: #fff2f0;
    color: var(--carbon-danger);
    border-color: var(--carbon-danger);
  }
  
  &.status-pending {
    background-color: #f6ffed;
    color: var(--carbon-info);
    border-color: var(--carbon-info);
  }
}

// 快速操作项
.quick-action-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 16px;
  border: 1px solid var(--carbon-border-light);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--carbon-bg-primary);
  
  &:hover {
    border-color: var(--carbon-primary);
    background-color: #f0f9ff;
    transform: translateY(-2px);
    box-shadow: var(--carbon-shadow-base);
  }
  
  .action-icon {
    font-size: 32px;
    color: var(--carbon-primary);
    margin-bottom: 12px;
  }
  
  .action-label {
    font-size: 14px;
    color: var(--carbon-text-regular);
    font-weight: 500;
  }
}

// 结果列表项
.result-item {
  border: 1px solid var(--carbon-border-light);
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--carbon-bg-primary);
  
  &:hover {
    border-color: var(--carbon-primary);
    box-shadow: var(--carbon-shadow-base);
  }
  
  .item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 16px;
    
    .item-title {
      font-size: 18px;
      font-weight: 600;
      color: var(--carbon-text-primary);
      margin: 0;
    }
  }
  
  .item-content {
    .item-details {
      margin-bottom: 16px;
      
      .detail-row {
        display: flex;
        gap: 24px;
        margin-bottom: 8px;
        
        .detail-item {
          flex: 1;
          
          label {
            font-weight: 500;
            color: var(--carbon-text-regular);
            margin-right: 8px;
          }
          
          span {
            color: var(--carbon-text-primary);
          }
        }
      }
    }
    
    .carbon-data {
      background: var(--carbon-bg-secondary);
      padding: 16px;
      border-radius: 6px;
      
      .carbon-stats {
        display: flex;
        justify-content: space-around;
        
        .carbon-stat {
          text-align: center;
          
          .stat-value {
            font-size: 20px;
            font-weight: bold;
            color: var(--carbon-primary);
            margin-bottom: 4px;
          }
          
          .stat-label {
            font-size: 12px;
            color: var(--carbon-text-secondary);
          }
        }
      }
    }
  }
  
  .item-actions {
    display: flex;
    gap: 8px;
    margin-top: 16px;
    justify-content: flex-end;
  }
}

// 分页样式
.pagination-wrapper {
  margin-top: 24px;
  text-align: center;
  
  .el-pagination {
    justify-content: center;
  }
}

// 空状态样式
.empty-state {
  text-align: center;
  padding: 60px 20px;
  
  .empty-icon {
    font-size: 64px;
    color: var(--carbon-text-placeholder);
    margin-bottom: 16px;
  }
  
  .empty-text {
    font-size: 16px;
    color: var(--carbon-text-secondary);
    margin-bottom: 24px;
  }
}

// 响应式设计
@media (max-width: 768px) {
  .carbon-admin-layout {
    .page-header {
      flex-direction: column;
      gap: 16px;
      
      .header-title {
        align-self: flex-start;
      }
      
      .header-actions {
        align-self: flex-end;
      }
    }
  }
  
  .search-area {
    padding: 16px;
  }
  
  .action-area {
    flex-direction: column;
    gap: 16px;
    
    .action-left,
    .action-right {
      width: 100%;
      justify-content: center;
    }
  }
  
  .result-item {
    .item-content {
      .item-details {
        .detail-row {
          flex-direction: column;
          gap: 8px;
        }
      }
      
      .carbon-data {
        .carbon-stats {
          flex-direction: column;
          gap: 16px;
        }
      }
    }
  }
}

// 动画效果
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.3s ease-out;
}

// 加载状态
.loading-overlay {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
  }
}
