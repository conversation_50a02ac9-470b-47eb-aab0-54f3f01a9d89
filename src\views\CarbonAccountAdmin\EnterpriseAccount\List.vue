<template>
  <div class="carbon-account-list carbon-admin-layout">
    <ContentWrap>
      <!-- 搜索区域 -->
      <div class="search-area">
        <ElForm :model="searchForm" inline>
          <ElFormItem label="企业名称">
            <ElInput
              v-model="searchForm.enterpriseName"
              placeholder="请输入企业名称"
              clearable
              style="width: 200px"
            />
          </ElFormItem>
          <ElFormItem label="账户状态">
            <ElSelect
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 150px"
            >
              <ElOption label="全部" value="" />
              <ElOption label="正常" value="1" />
              <ElOption label="冻结" value="2" />
              <ElOption label="注销" value="3" />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="注册时间">
            <ElDatePicker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </ElFormItem>
          <ElFormItem>
            <ElButton type="primary" @click="handleSearch">
              <Icon icon="ep:search" class="mr-5px" />
              搜索
            </ElButton>
            <ElButton @click="handleReset">
              <Icon icon="ep:refresh" class="mr-5px" />
              重置
            </ElButton>
          </ElFormItem>
        </ElForm>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-area">
        <ElButton type="primary" @click="handleAdd">
          <Icon icon="ep:plus" class="mr-5px" />
          新增企业账户
        </ElButton>
        <ElButton type="success" @click="handleExport">
          <Icon icon="ep:download" class="mr-5px" />
          导出数据
        </ElButton>
      </div>

      <!-- 表格区域 -->
      <Table
        v-model:pageSize="tableObject.pageSize"
        v-model:currentPage="tableObject.currentPage"
        :columns="columns"
        :data="tableObject.tableList"
        :loading="tableObject.loading"
        :pagination="{
          total: tableObject.total
        }"
        @register="register"
      >
        <template #status="{ row }">
          <ElTag :type="getStatusType(row.status)">
            {{ getStatusText(row.status) }}
          </ElTag>
        </template>
        <template #action="{ row }">
          <ElButton type="primary" link @click="handleDetail(row)">
            <Icon icon="ep:view" class="mr-5px" />
            查看详情
          </ElButton>
          <ElButton type="warning" link @click="handleEdit(row)">
            <Icon icon="ep:edit" class="mr-5px" />
            编辑
          </ElButton>
          <ElButton
            type="danger"
            link
            @click="handleDelete(row)"
            v-if="row.status !== '3'"
          >
            <Icon icon="ep:delete" class="mr-5px" />
            删除
          </ElButton>
        </template>
      </Table>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Table } from '@/components/Table'
import { useTable } from '@/hooks/web/useTable'
import { Icon } from '@/components/Icon'

const router = useRouter()

// 搜索表单
const searchForm = reactive({
  enterpriseName: '',
  status: '',
  dateRange: []
})

// 表格配置
const columns = [
  {
    field: 'index',
    label: '序号',
    type: 'index',
    width: 80
  },
  {
    field: 'enterpriseName',
    label: '企业名称',
    minWidth: 200
  },
  {
    field: 'creditCode',
    label: '统一社会信用代码',
    minWidth: 180
  },
  {
    field: 'legalPerson',
    label: '法定代表人',
    width: 120
  },
  {
    field: 'contactPhone',
    label: '联系电话',
    width: 130
  },
  {
    field: 'registrationTime',
    label: '注册时间',
    width: 120
  },
  {
    field: 'status',
    label: '账户状态',
    width: 100,
    slots: {
      default: 'status'
    }
  },
  {
    field: 'action',
    label: '操作',
    width: 200,
    slots: {
      default: 'action'
    }
  }
]

// 表格钩子
const { register, tableObject, methods } = useTable({
  getListApi: getEnterpriseAccountList,
  delListApi: deleteEnterpriseAccount
})

const { getList } = methods

// 模拟API
async function getEnterpriseAccountList(params: any) {
  // 这里应该调用真实的API
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = {
        list: [
          {
            id: '1',
            enterpriseName: '绿色科技有限公司',
            creditCode: '91110000123456789X',
            legalPerson: '张三',
            contactPhone: '***********',
            registrationTime: '2024-01-15',
            status: '1'
          },
          {
            id: '2',
            enterpriseName: '环保能源股份有限公司',
            creditCode: '91110000987654321Y',
            legalPerson: '李四',
            contactPhone: '***********',
            registrationTime: '2024-02-20',
            status: '1'
          },
          {
            id: '3',
            enterpriseName: '清洁技术发展有限公司',
            creditCode: '91110000456789123Z',
            legalPerson: '王五',
            contactPhone: '***********',
            registrationTime: '2024-03-10',
            status: '2'
          }
        ],
        total: 3
      }
      resolve(mockData)
    }, 500)
  })
}

async function deleteEnterpriseAccount(ids: string[]) {
  // 模拟删除API
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true)
    }, 500)
  })
}

// 状态相关方法
const getStatusType = (status: string) => {
  const statusMap = {
    '1': 'success',
    '2': 'warning',
    '3': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    '1': '正常',
    '2': '冻结',
    '3': '注销'
  }
  return statusMap[status] || '未知'
}

// 事件处理方法
const handleSearch = () => {
  getList()
}

const handleReset = () => {
  Object.assign(searchForm, {
    enterpriseName: '',
    status: '',
    dateRange: []
  })
  getList()
}

const handleAdd = () => {
  // 跳转到新增页面或打开弹窗
  ElMessage.info('新增功能待实现')
}

const handleExport = () => {
  ElMessage.info('导出功能待实现')
}

const handleDetail = (row: any) => {
  router.push(`/carbon-account-admin/enterprise-account/detail/${row.id}`)
}

const handleEdit = (row: any) => {
  ElMessage.info('编辑功能待实现')
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm('确定要删除这个企业账户吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    await methods.delList([row.id])
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    // 用户取消删除
  }
}

onMounted(() => {
  getList()
})
</script>

<style lang="less" scoped>
.carbon-account-list {
  .search-area {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
  }
  
  .action-area {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
