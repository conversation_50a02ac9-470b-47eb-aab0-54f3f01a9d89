# 多阶段构建 Dockerfile
# 第一阶段：构建阶段
FROM node:18-alpine AS builder

# 设置工作目录
WORKDIR /app

# 安装 pnpm
RUN npm install -g pnpm

# 复制 package.json
COPY package.json ./

# 复制锁文件（如果存在）
COPY pnpm-lock.yaml* yarn.lock* package-lock.json* ./

# 安装依赖 - 根据锁文件选择包管理器
RUN if [ -f pnpm-lock.yaml ]; then \
        pnpm install; \
    elif [ -f yarn.lock ]; then \
        npm install -g yarn && yarn install --frozen-lockfile; \
    else \
        npm install; \
    fi

# 复制源代码
COPY . .

# 构建应用 - 根据可用的包管理器选择构建命令
RUN if [ -f pnpm-lock.yaml ]; then \
        pnpm run build:pro; \
    elif [ -f yarn.lock ]; then \
        yarn build:pro; \
    else \
        npm run build:pro; \
    fi

# 第二阶段：运行阶段
FROM nginx:alpine

# 安装 tzdata 用于时区设置
RUN apk add --no-cache tzdata

# 设置时区为中国标准时间
ENV TZ=Asia/Shanghai
RUN ln -snf /usr/share/zoneinfo/$TZ /etc/localtime && echo $TZ > /etc/timezone

# 创建 nginx 用户和组
RUN addgroup -g 1001 -S nginx && \
    adduser -S -D -H -u 1001 -h /var/cache/nginx -s /sbin/nologin -G nginx -g nginx nginx

# 复制构建产物到 nginx 目录
COPY --from=builder /app/dist-pro /usr/share/nginx/html

# 复制 nginx 配置文件
COPY docker/nginx.conf /etc/nginx/nginx.conf

# 创建日志目录
RUN mkdir -p /var/log/nginx && \
    chown -R nginx:nginx /var/log/nginx && \
    chown -R nginx:nginx /usr/share/nginx/html

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost/ || exit 1

# 启动 nginx
CMD ["nginx", "-g", "daemon off;"]
