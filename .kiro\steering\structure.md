# Project Structure

## Root Directory
```
├── src/                    # Main source code
├── public/                 # Static assets
├── mock/                   # Mock data for development
├── types/                  # TypeScript type definitions
├── scripts/                # Build and utility scripts
├── plop/                   # Code generation templates
├── .husky/                 # Git hooks configuration
├── .kiro/                  # Kiro AI assistant configuration
└── config files           # Various configuration files
```

## Source Code Organization (`src/`)

### Core Application Files
- `main.ts` - Application entry point with plugin setup
- `App.vue` - Root Vue component
- `permission.ts` - Route permission guards

### Directory Structure
```
src/
├── api/                    # API service layer
├── assets/                 # Static assets (images, fonts, etc.)
├── axios/                  # HTTP client configuration
├── components/             # Reusable Vue components
├── constants/              # Application constants
├── directives/             # Custom Vue directives
├── hooks/                  # Composable functions
├── layout/                 # Layout components
├── locales/                # Internationalization files
├── plugins/                # Vue plugins setup
├── router/                 # Vue Router configuration
├── store/                  # Pinia state management
├── styles/                 # Global styles and variables
├── utils/                  # Utility functions
└── views/                  # Page components
```

## Key Architectural Patterns

### Component Organization
- **Global Components** - Registered in `src/components/` and auto-imported
- **Page Components** - Located in `src/views/` following route structure
- **Layout Components** - Reusable layout templates in `src/layout/`

### State Management
- **Pinia Stores** - Modular stores in `src/store/modules/`
- **Persistence** - Automatic state persistence with `pinia-plugin-persistedstate`
- **Store Structure** - Each module handles specific domain logic

### Routing Structure
- **Dynamic Routes** - Permission-based route generation
- **Nested Routes** - Support for complex page hierarchies
- **Route Guards** - Authentication and authorization checks

### API Layer
- **Service Files** - API endpoints organized by feature in `src/api/`
- **Axios Interceptors** - Request/response handling in `src/axios/`
- **Mock Integration** - Development mocking in `mock/` directory

### Styling Architecture
- **Global Styles** - Base styles in `src/styles/`
- **Component Styles** - Scoped styles using Less preprocessor
- **Theme Variables** - Centralized in `src/styles/variables.module.less`
- **UnoCSS Utilities** - Atomic CSS classes for rapid development

## File Naming Conventions
- **Components** - PascalCase (e.g., `UserProfile.vue`)
- **Views/Pages** - PascalCase (e.g., `UserManagement.vue`)
- **Composables** - camelCase with `use` prefix (e.g., `useUserData.ts`)
- **Utilities** - camelCase (e.g., `formatDate.ts`)
- **Constants** - UPPER_SNAKE_CASE (e.g., `API_ENDPOINTS.ts`)

## Import Path Aliases
- `@/` - Points to `src/` directory
- Absolute imports preferred over relative imports
- TypeScript path mapping configured in `tsconfig.json`

## Code Generation
- **Plop Templates** - Component and view generators in `plop/`
- **Usage** - Run `pnpm run p` to generate new components/views
- **Consistency** - Ensures consistent file structure and naming

## Environment-Specific Configuration
- **Base Config** - `.env.base` for common settings
- **Environment Files** - Separate configs for dev, test, pro, gitee
- **Build Outputs** - Different output directories per environment
- **Feature Flags** - Environment variables control feature availability

## Mock Data Structure
- **Feature-Based** - Mock files organized by application feature
- **Development Only** - Automatically disabled in production builds
- **API Matching** - Mock endpoints mirror real API structure