-- 碳账户管理系统数据库初始化脚本

-- 创建数据库（如果不存在）
-- CREATE DATABASE IF NOT EXISTS carbon_account;

-- 使用数据库
-- \c carbon_account;

-- 创建扩展
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 企业信息表
CREATE TABLE IF NOT EXISTS enterprises (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    enterprise_name VARCHAR(255) NOT NULL,
    credit_code VARCHAR(50) UNIQUE NOT NULL,
    legal_person VARCHAR(100) NOT NULL,
    registered_capital VARCHAR(100),
    industry VARCHAR(100),
    scale VARCHAR(50),
    establishment_date DATE,
    registered_address TEXT,
    business_scope TEXT,
    contact_person VARCHAR(100),
    contact_phone VARCHAR(20),
    contact_email VARCHAR(100),
    status INTEGER DEFAULT 1, -- 1:正常 2:冻结 3:注销
    carbon_score INTEGER DEFAULT 0,
    total_emissions DECIMAL(15,2) DEFAULT 0,
    registration_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    last_login_time TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户表
CREATE TABLE IF NOT EXISTS users (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    real_name VARCHAR(100),
    email VARCHAR(100),
    phone VARCHAR(20),
    avatar_url VARCHAR(255),
    enterprise_id UUID REFERENCES enterprises(id),
    status INTEGER DEFAULT 1, -- 1:正常 2:禁用
    last_login_time TIMESTAMP,
    login_count INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 角色表
CREATE TABLE IF NOT EXISTS roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    role_name VARCHAR(100) NOT NULL,
    role_code VARCHAR(50) UNIQUE NOT NULL,
    description TEXT,
    is_system BOOLEAN DEFAULT FALSE,
    status INTEGER DEFAULT 1, -- 1:启用 2:禁用
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 权限表
CREATE TABLE IF NOT EXISTS permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    permission_name VARCHAR(100) NOT NULL,
    permission_code VARCHAR(50) UNIQUE NOT NULL,
    parent_id UUID REFERENCES permissions(id),
    permission_type INTEGER DEFAULT 1, -- 1:菜单 2:按钮 3:接口
    sort_order INTEGER DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 用户角色关联表
CREATE TABLE IF NOT EXISTS user_roles (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id) ON DELETE CASCADE,
    role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(user_id, role_id)
);

-- 角色权限关联表
CREATE TABLE IF NOT EXISTS role_permissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    role_id UUID REFERENCES roles(id) ON DELETE CASCADE,
    permission_id UUID REFERENCES permissions(id) ON DELETE CASCADE,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(role_id, permission_id)
);

-- 企业注册申请表
CREATE TABLE IF NOT EXISTS registration_applications (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    enterprise_name VARCHAR(255) NOT NULL,
    credit_code VARCHAR(50) NOT NULL,
    legal_person VARCHAR(100) NOT NULL,
    industry VARCHAR(100),
    applicant_name VARCHAR(100) NOT NULL,
    applicant_phone VARCHAR(20) NOT NULL,
    applicant_email VARCHAR(100),
    application_documents JSONB, -- 存储文档信息
    status INTEGER DEFAULT 0, -- 0:待审核 1:已通过 2:已拒绝
    audit_time TIMESTAMP,
    auditor VARCHAR(100),
    audit_reason TEXT,
    application_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 碳排放数据表
CREATE TABLE IF NOT EXISTS carbon_emissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    enterprise_id UUID REFERENCES enterprises(id) ON DELETE CASCADE,
    year INTEGER NOT NULL,
    scope1_emissions DECIMAL(15,2) DEFAULT 0, -- 直接排放
    scope2_emissions DECIMAL(15,2) DEFAULT 0, -- 间接排放（电力）
    scope3_emissions DECIMAL(15,2) DEFAULT 0, -- 其他间接排放
    total_emissions DECIMAL(15,2) DEFAULT 0,
    reduction_rate DECIMAL(5,2) DEFAULT 0,
    energy_consumption DECIMAL(15,2) DEFAULT 0,
    data_source VARCHAR(100),
    verification_status INTEGER DEFAULT 0, -- 0:未验证 1:已验证
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    UNIQUE(enterprise_id, year)
);

-- 减排措施表
CREATE TABLE IF NOT EXISTS reduction_measures (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    enterprise_id UUID REFERENCES enterprises(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    expected_reduction DECIMAL(15,2) DEFAULT 0,
    actual_reduction DECIMAL(15,2) DEFAULT 0,
    investment DECIMAL(15,2) DEFAULT 0,
    implement_time DATE,
    completion_time DATE,
    status INTEGER DEFAULT 0, -- 0:计划中 1:实施中 2:已完成 3:已取消
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 操作日志表
CREATE TABLE IF NOT EXISTS operation_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    user_id UUID REFERENCES users(id),
    operation_type VARCHAR(50) NOT NULL,
    operation_desc TEXT,
    target_type VARCHAR(50),
    target_id UUID,
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 创建索引
CREATE INDEX IF NOT EXISTS idx_enterprises_credit_code ON enterprises(credit_code);
CREATE INDEX IF NOT EXISTS idx_enterprises_status ON enterprises(status);
CREATE INDEX IF NOT EXISTS idx_users_username ON users(username);
CREATE INDEX IF NOT EXISTS idx_users_enterprise_id ON users(enterprise_id);
CREATE INDEX IF NOT EXISTS idx_carbon_emissions_enterprise_year ON carbon_emissions(enterprise_id, year);
CREATE INDEX IF NOT EXISTS idx_operation_logs_user_id ON operation_logs(user_id);
CREATE INDEX IF NOT EXISTS idx_operation_logs_created_at ON operation_logs(created_at);

-- 插入初始数据
INSERT INTO roles (role_name, role_code, description, is_system) VALUES
('超级管理员', 'super_admin', '系统超级管理员，拥有所有权限', TRUE),
('企业管理员', 'enterprise_admin', '企业管理员，负责企业内部用户和数据管理', FALSE),
('数据操作员', 'data_operator', '数据操作员，负责数据录入和基础操作', FALSE),
('数据查看员', 'data_viewer', '数据查看员，只能查看相关数据', FALSE)
ON CONFLICT (role_code) DO NOTHING;

INSERT INTO permissions (permission_name, permission_code, parent_id, permission_type) VALUES
('系统管理', 'system_management', NULL, 1),
('企业管理', 'enterprise_management', NULL, 1),
('数据管理', 'data_management', NULL, 1),
('报表管理', 'report_management', NULL, 1)
ON CONFLICT (permission_code) DO NOTHING;

-- 创建更新时间触发器函数
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ language 'plpgsql';

-- 为需要的表创建更新时间触发器
CREATE TRIGGER update_enterprises_updated_at BEFORE UPDATE ON enterprises
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON users
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_roles_updated_at BEFORE UPDATE ON roles
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_registration_applications_updated_at BEFORE UPDATE ON registration_applications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_reduction_measures_updated_at BEFORE UPDATE ON reduction_measures
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
