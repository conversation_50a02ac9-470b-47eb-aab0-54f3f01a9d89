# Technology Stack

## Core Framework
- **Vue 3** - Composition API with `<script setup>` syntax
- **TypeScript** - Strict typing enabled
- **Vite 6** - Build tool and dev server
- **Element Plus 2.9** - UI component library
- **Vue Router 4** - Client-side routing
- **Pinia** - State management with persistence

## Build & Development Tools
- **pnpm** - Package manager (required >= 8.1.0)
- **Node.js** - Runtime (required >= 18.0.0)
- **ESLint 9** - Code linting with TypeScript and Vue support
- **Prettier** - Code formatting
- **Stylelint** - CSS/Less linting
- **Husky** - Git hooks for pre-commit checks
- **UnoCSS** - Atomic CSS framework

## Styling & UI
- **Less** - CSS preprocessor
- **UnoCSS** - Utility-first CSS framework
- **Element Plus** - Component library with theme customization
- **Animate.css** - CSS animations
- **Iconify** - Icon system with SVG support

## Development Features
- **Vite Plugin Mock** - API mocking for development
- **Vue I18n** - Internationalization
- **Monaco Editor** - Code editor integration
- **ECharts** - Data visualization charts
- **Axios** - HTTP client with interceptors

## Common Commands

### Development
```bash
# Install dependencies
pnpm install

# Start development server
pnpm run dev

# Type checking
pnpm run ts:check
```

### Building
```bash
# Production build
pnpm run build:pro

# Development build
pnpm run build:dev

# Test environment build
pnpm run build:test

# Gitee build
pnpm run build:gitee
```

### Code Quality
```bash
# ESLint check and fix
pnpm run lint:eslint

# Format code with Prettier
pnpm run lint:format

# Stylelint check and fix
pnpm run lint:style

# Run all linting (used in pre-commit)
pnpm run lint:lint-staged
```

### Utilities
```bash
# Generate components/views with Plop
pnpm run p

# Generate SVG icons
pnpm run icon

# Check for package updates
pnpm run npm:check

# Clean node_modules
pnpm run clean
```

## Environment Configuration
- Multiple environment files (`.env.base`, `.env.dev`, `.env.pro`, `.env.test`)
- Environment-specific builds with different optimization levels
- Configurable features via environment variables (mock, icons, styling, etc.)

## Code Style Rules
- **No semicolons** - Prettier configured without semicolons
- **Single quotes** - Preferred over double quotes
- **2-space indentation** - Consistent across all files
- **100 character line limit** - For better readability
- **Composition API** - Use `<script setup>` syntax for Vue components
- **TypeScript strict mode** - All type checking enabled