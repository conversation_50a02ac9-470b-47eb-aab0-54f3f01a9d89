version: '3.8'

services:
  # 前端应用 - 简化版本
  carbon-account-frontend:
    build:
      context: .
      dockerfile: Dockerfile.simple
    container_name: carbon-account-frontend
    restart: unless-stopped
    ports:
      - "80:80"
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf:ro
    networks:
      - carbon-network
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  carbon-network:
    driver: bridge
