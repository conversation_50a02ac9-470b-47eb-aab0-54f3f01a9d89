<template>
  <div class="role-detail">
    <ContentWrap>
      <!-- 页面头部 -->
      <div class="page-header mb-20px">
        <ElButton @click="handleBack">
          <Icon icon="ep:arrow-left" class="mr-5px" />
          返回列表
        </ElButton>
        <div class="header-title">
          <h2>角色详情</h2>
          <ElTag :type="getRoleStatusType(roleInfo.status)" size="large">
            {{ getRoleStatusText(roleInfo.status) }}
          </ElTag>
        </div>
      </div>

      <!-- 基本信息 -->
      <ElCard class="mb-20px">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:key" class="mr-5px" />
            基本信息
          </div>
        </template>
        <ElRow :gutter="20">
          <ElCol :span="12">
            <div class="info-item">
              <label>角色ID：</label>
              <span>{{ roleInfo.id }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>角色名称：</label>
              <span>{{ roleInfo.roleName }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>角色编码：</label>
              <span>{{ roleInfo.roleCode }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>创建时间：</label>
              <span>{{ roleInfo.createTime }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>更新时间：</label>
              <span>{{ roleInfo.updateTime }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>用户数量：</label>
              <span>{{ roleInfo.userCount }}人</span>
            </div>
          </ElCol>
          <ElCol :span="24">
            <div class="info-item">
              <label>角色描述：</label>
              <span>{{ roleInfo.description }}</span>
            </div>
          </ElCol>
        </ElRow>
      </ElCard>

      <!-- 权限配置 -->
      <ElCard class="mb-20px">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:lock" class="mr-5px" />
            权限配置
          </div>
        </template>
        <div class="permissions-section">
          <ElTree
            ref="permissionTreeRef"
            :data="permissionTree"
            show-checkbox
            node-key="id"
            :default-checked-keys="roleInfo.checkedPermissions"
            :props="{ children: 'children', label: 'name' }"
            :disabled="!editMode"
            class="permission-tree"
          />
          <div class="permission-actions mt-20px" v-if="editMode">
            <ElButton @click="handleExpandAll">展开全部</ElButton>
            <ElButton @click="handleCollapseAll">收起全部</ElButton>
            <ElButton @click="handleCheckAll">全选</ElButton>
            <ElButton @click="handleUncheckAll">取消全选</ElButton>
          </div>
        </div>
      </ElCard>

      <!-- 关联用户 -->
      <ElCard class="mb-20px">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:user" class="mr-5px" />
            关联用户
          </div>
        </template>
        <ElTable :data="roleUsers" style="width: 100%">
          <ElTableColumn prop="userName" label="用户姓名" width="120" />
          <ElTableColumn prop="account" label="登录账号" width="150" />
          <ElTableColumn prop="enterpriseName" label="所属企业" min-width="200" />
          <ElTableColumn prop="phone" label="手机号码" width="130" />
          <ElTableColumn prop="email" label="邮箱地址" min-width="180" />
          <ElTableColumn prop="lastLoginTime" label="最后登录" width="150" />
          <ElTableColumn prop="status" label="状态" width="80">
            <template #default="{ row }">
              <ElTag :type="row.status === '1' ? 'success' : 'danger'">
                {{ row.status === '1' ? '正常' : '禁用' }}
              </ElTag>
            </template>
          </ElTableColumn>
          <ElTableColumn label="操作" width="120">
            <template #default="{ row }">
              <ElButton type="primary" link @click="handleViewUser(row)">
                查看详情
              </ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </ElCard>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <ElButton v-if="!editMode" type="primary" @click="handleEdit">
          <Icon icon="ep:edit" class="mr-5px" />
          编辑角色
        </ElButton>
        <template v-else>
          <ElButton type="primary" @click="handleSave">
            <Icon icon="ep:check" class="mr-5px" />
            保存修改
          </ElButton>
          <ElButton @click="handleCancel">
            <Icon icon="ep:close" class="mr-5px" />
            取消编辑
          </ElButton>
        </template>
        <ElButton
          :type="roleInfo.status === '1' ? 'warning' : 'success'"
          @click="handleToggleStatus"
        >
          <Icon :icon="roleInfo.status === '1' ? 'ep:lock' : 'ep:unlock'" class="mr-5px" />
          {{ roleInfo.status === '1' ? '禁用角色' : '启用角色' }}
        </ElButton>
        <ElButton type="danger" @click="handleDelete" v-if="!roleInfo.isSystem">
          <Icon icon="ep:delete" class="mr-5px" />
          删除角色
        </ElButton>
      </div>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'

const router = useRouter()
const route = useRoute()

// 编辑模式
const editMode = ref(false)

// 权限树引用
const permissionTreeRef = ref()

// 角色信息
const roleInfo = ref({
  id: '',
  roleName: '',
  roleCode: '',
  description: '',
  createTime: '',
  updateTime: '',
  userCount: 0,
  status: '1',
  isSystem: false,
  checkedPermissions: [] as string[]
})

// 权限树数据
const permissionTree = ref([
  {
    id: '1',
    name: '系统管理',
    children: [
      { id: '11', name: '用户管理' },
      { id: '12', name: '角色管理' },
      { id: '13', name: '权限管理' },
      { id: '14', name: '系统设置' }
    ]
  },
  {
    id: '2',
    name: '企业管理',
    children: [
      { id: '21', name: '企业信息' },
      { id: '22', name: '企业用户' },
      { id: '23', name: '企业审核' }
    ]
  },
  {
    id: '3',
    name: '数据管理',
    children: [
      { id: '31', name: '数据查看' },
      { id: '32', name: '数据编辑' },
      { id: '33', name: '数据导出' },
      { id: '34', name: '数据导入' }
    ]
  },
  {
    id: '4',
    name: '报表管理',
    children: [
      { id: '41', name: '报表查看' },
      { id: '42', name: '报表生成' },
      { id: '43', name: '报表导出' }
    ]
  }
])

// 关联用户
const roleUsers = ref([
  {
    id: '1',
    userName: '张三',
    account: 'zhangsan',
    enterpriseName: '绿色科技有限公司',
    phone: '***********',
    email: '<EMAIL>',
    lastLoginTime: '2024-07-28 16:45:00',
    status: '1'
  },
  {
    id: '2',
    userName: '李四',
    account: 'lisi',
    enterpriseName: '环保能源股份有限公司',
    phone: '***********',
    email: '<EMAIL>',
    lastLoginTime: '2024-07-27 14:30:00',
    status: '1'
  }
])

// 状态相关方法
const getRoleStatusType = (status: string) => {
  const statusMap = {
    '1': 'success',
    '2': 'danger'
  }
  return statusMap[status] || 'info'
}

const getRoleStatusText = (status: string) => {
  const statusMap = {
    '1': '启用',
    '2': '禁用'
  }
  return statusMap[status] || '未知'
}

// 获取角色详情
const getRoleDetail = async () => {
  const id = route.params.id as string
  // 模拟API调用
  setTimeout(() => {
    roleInfo.value = {
      id: id,
      roleName: '企业管理员',
      roleCode: 'enterprise_admin',
      description: '企业管理员，负责企业内部用户和数据管理',
      createTime: '2024-01-15 10:30:00',
      updateTime: '2024-07-20 14:20:00',
      userCount: 5,
      status: '1',
      isSystem: false,
      checkedPermissions: ['21', '22', '23', '31', '32', '41']
    }
  }, 500)
}

// 权限树操作方法
const handleExpandAll = () => {
  // 展开所有节点
  const allKeys = getAllNodeKeys(permissionTree.value)
  permissionTreeRef.value?.setExpandedKeys(allKeys)
}

const handleCollapseAll = () => {
  // 收起所有节点
  permissionTreeRef.value?.setExpandedKeys([])
}

const handleCheckAll = () => {
  // 全选所有节点
  const allKeys = getAllNodeKeys(permissionTree.value)
  permissionTreeRef.value?.setCheckedKeys(allKeys)
}

const handleUncheckAll = () => {
  // 取消全选
  permissionTreeRef.value?.setCheckedKeys([])
}

const getAllNodeKeys = (nodes: any[]): string[] => {
  const keys: string[] = []
  const traverse = (nodeList: any[]) => {
    nodeList.forEach(node => {
      keys.push(node.id)
      if (node.children && node.children.length > 0) {
        traverse(node.children)
      }
    })
  }
  traverse(nodes)
  return keys
}

// 事件处理方法
const handleBack = () => {
  router.back()
}

const handleEdit = () => {
  editMode.value = true
}

const handleSave = async () => {
  try {
    // 获取选中的权限
    const checkedKeys = permissionTreeRef.value?.getCheckedKeys() || []
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    roleInfo.value.checkedPermissions = checkedKeys
    editMode.value = false
    ElMessage.success('保存成功')
  } catch (error) {
    ElMessage.error('保存失败')
  }
}

const handleCancel = () => {
  editMode.value = false
  // 重置权限选择
  permissionTreeRef.value?.setCheckedKeys(roleInfo.value.checkedPermissions)
}

const handleToggleStatus = async () => {
  const action = roleInfo.value.status === '1' ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(`确定要${action}角色"${roleInfo.value.roleName}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 模拟API调用
    roleInfo.value.status = roleInfo.value.status === '1' ? '2' : '1'
    ElMessage.success(`${action}成功`)
  } catch (error) {
    // 用户取消
  }
}

const handleDelete = async () => {
  if (roleInfo.value.userCount > 0) {
    ElMessage.warning('该角色下还有用户，无法删除')
    return
  }
  
  try {
    await ElMessageBox.confirm(`确定要删除角色"${roleInfo.value.roleName}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    })
    
    ElMessage.success('删除成功')
    router.back()
  } catch (error) {
    // 用户取消
  }
}

const handleViewUser = (row: any) => {
  router.push(`/carbon-account-admin/enterprise-account/user-detail/${row.id}`)
}

onMounted(() => {
  getRoleDetail()
})
</script>

<style lang="less" scoped>
.role-detail {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .header-title {
      display: flex;
      align-items: center;
      gap: 15px;
      
      h2 {
        margin: 0;
        color: #303133;
      }
    }
  }
  
  .card-header {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #303133;
  }
  
  .info-item {
    margin-bottom: 15px;
    
    label {
      font-weight: 600;
      color: #606266;
      margin-right: 10px;
    }
    
    span {
      color: #303133;
    }
  }
  
  .permissions-section {
    .permission-tree {
      border: 1px solid #ebeef5;
      border-radius: 4px;
      padding: 15px;
      max-height: 400px;
      overflow-y: auto;
    }
    
    .permission-actions {
      display: flex;
      gap: 10px;
    }
  }
  
  .action-buttons {
    text-align: center;
    
    .el-button {
      margin: 0 10px;
    }
  }
}
</style>
