<template>
  <div class="user-list">
    <ContentWrap>
      <!-- 搜索区域 -->
      <div class="search-area mb-20px">
        <ElForm :model="searchForm" inline>
          <ElFormItem label="用户姓名">
            <ElInput
              v-model="searchForm.userName"
              placeholder="请输入用户姓名"
              clearable
              style="width: 200px"
            />
          </ElFormItem>
          <ElFormItem label="所属企业">
            <ElSelect
              v-model="searchForm.enterpriseId"
              placeholder="请选择企业"
              clearable
              style="width: 200px"
            >
              <ElOption label="全部" value="" />
              <ElOption label="绿色科技有限公司" value="1" />
              <ElOption label="环保能源股份有限公司" value="2" />
              <ElOption label="清洁技术发展有限公司" value="3" />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="用户状态">
            <ElSelect
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 150px"
            >
              <ElOption label="全部" value="" />
              <ElOption label="正常" value="1" />
              <ElOption label="禁用" value="2" />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="用户角色">
            <ElSelect
              v-model="searchForm.roleId"
              placeholder="请选择角色"
              clearable
              style="width: 150px"
            >
              <ElOption label="全部" value="" />
              <ElOption label="管理员" value="1" />
              <ElOption label="操作员" value="2" />
              <ElOption label="查看员" value="3" />
            </ElSelect>
          </ElFormItem>
          <ElFormItem>
            <ElButton type="primary" @click="handleSearch">
              <Icon icon="ep:search" class="mr-5px" />
              搜索
            </ElButton>
            <ElButton @click="handleReset">
              <Icon icon="ep:refresh" class="mr-5px" />
              重置
            </ElButton>
          </ElFormItem>
        </ElForm>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-area mb-20px">
        <ElButton type="primary" @click="handleAdd">
          <Icon icon="ep:plus" class="mr-5px" />
          新增用户
        </ElButton>
        <ElButton type="success" @click="handleBatchEnable" :disabled="!selectedIds.length">
          <Icon icon="ep:check" class="mr-5px" />
          批量启用
        </ElButton>
        <ElButton type="warning" @click="handleBatchDisable" :disabled="!selectedIds.length">
          <Icon icon="ep:close" class="mr-5px" />
          批量禁用
        </ElButton>
        <ElButton type="danger" @click="handleBatchDelete" :disabled="!selectedIds.length">
          <Icon icon="ep:delete" class="mr-5px" />
          批量删除
        </ElButton>
      </div>

      <!-- 表格区域 -->
      <Table
        v-model:pageSize="tableObject.pageSize"
        v-model:currentPage="tableObject.currentPage"
        :columns="columns"
        :data="tableObject.tableList"
        :loading="tableObject.loading"
        :pagination="{
          total: tableObject.total
        }"
        @register="register"
        @selection-change="handleSelectionChange"
      >
        <template #avatar="{ row }">
          <ElAvatar :src="row.avatar" :size="40">
            {{ row.userName.charAt(0) }}
          </ElAvatar>
        </template>
        <template #status="{ row }">
          <ElTag :type="getUserStatusType(row.status)">
            {{ getUserStatusText(row.status) }}
          </ElTag>
        </template>
        <template #roles="{ row }">
          <ElTag
            v-for="role in row.roles"
            :key="role.id"
            size="small"
            class="mr-5px"
          >
            {{ role.name }}
          </ElTag>
        </template>
        <template #action="{ row }">
          <ElButton type="primary" link @click="handleDetail(row)">
            <Icon icon="ep:view" class="mr-5px" />
            查看详情
          </ElButton>
          <ElButton type="warning" link @click="handleEdit(row)">
            <Icon icon="ep:edit" class="mr-5px" />
            编辑
          </ElButton>
          <ElButton
            :type="row.status === '1' ? 'warning' : 'success'"
            link
            @click="handleToggleStatus(row)"
          >
            <Icon :icon="row.status === '1' ? 'ep:lock' : 'ep:unlock'" class="mr-5px" />
            {{ row.status === '1' ? '禁用' : '启用' }}
          </ElButton>
          <ElButton type="danger" link @click="handleDelete(row)">
            <Icon icon="ep:delete" class="mr-5px" />
            删除
          </ElButton>
        </template>
      </Table>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Table } from '@/components/Table'
import { useTable } from '@/hooks/web/useTable'
import { Icon } from '@/components/Icon'

const router = useRouter()

// 搜索表单
const searchForm = reactive({
  userName: '',
  enterpriseId: '',
  status: '',
  roleId: ''
})

// 选中的用户ID
const selectedIds = ref<string[]>([])

// 表格配置
const columns = [
  {
    field: 'selection',
    type: 'selection',
    width: 60
  },
  {
    field: 'index',
    label: '序号',
    type: 'index',
    width: 80
  },
  {
    field: 'avatar',
    label: '头像',
    width: 80,
    slots: {
      default: 'avatar'
    }
  },
  {
    field: 'userName',
    label: '用户姓名',
    width: 120
  },
  {
    field: 'account',
    label: '登录账号',
    width: 150
  },
  {
    field: 'enterpriseName',
    label: '所属企业',
    minWidth: 200
  },
  {
    field: 'phone',
    label: '手机号码',
    width: 130
  },
  {
    field: 'email',
    label: '邮箱地址',
    minWidth: 180
  },
  {
    field: 'roles',
    label: '用户角色',
    width: 150,
    slots: {
      default: 'roles'
    }
  },
  {
    field: 'lastLoginTime',
    label: '最后登录',
    width: 150
  },
  {
    field: 'status',
    label: '状态',
    width: 80,
    slots: {
      default: 'status'
    }
  },
  {
    field: 'action',
    label: '操作',
    width: 250,
    slots: {
      default: 'action'
    }
  }
]

// 表格钩子
const { register, tableObject, methods } = useTable({
  getListApi: getUserList,
  delListApi: deleteUser
})

const { getList } = methods

// 模拟API
async function getUserList(params: any) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = {
        list: [
          {
            id: '1',
            userName: '张三',
            account: 'zhangsan',
            enterpriseName: '绿色科技有限公司',
            phone: '***********',
            email: '<EMAIL>',
            avatar: '',
            roles: [
              { id: '1', name: '管理员' }
            ],
            lastLoginTime: '2024-07-28 16:45:00',
            status: '1'
          },
          {
            id: '2',
            userName: '李四',
            account: 'lisi',
            enterpriseName: '环保能源股份有限公司',
            phone: '***********',
            email: '<EMAIL>',
            avatar: '',
            roles: [
              { id: '2', name: '操作员' }
            ],
            lastLoginTime: '2024-07-27 14:30:00',
            status: '1'
          },
          {
            id: '3',
            userName: '王五',
            account: 'wangwu',
            enterpriseName: '清洁技术发展有限公司',
            phone: '***********',
            email: '<EMAIL>',
            avatar: '',
            roles: [
              { id: '3', name: '查看员' }
            ],
            lastLoginTime: '2024-07-26 10:15:00',
            status: '2'
          }
        ],
        total: 3
      }
      resolve(mockData)
    }, 500)
  })
}

async function deleteUser(ids: string[]) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true)
    }, 500)
  })
}

// 状态相关方法
const getUserStatusType = (status: string) => {
  const statusMap = {
    '1': 'success',
    '2': 'danger'
  }
  return statusMap[status] || 'info'
}

const getUserStatusText = (status: string) => {
  const statusMap = {
    '1': '正常',
    '2': '禁用'
  }
  return statusMap[status] || '未知'
}

// 事件处理方法
const handleSearch = () => {
  getList()
}

const handleReset = () => {
  Object.assign(searchForm, {
    userName: '',
    enterpriseId: '',
    status: '',
    roleId: ''
  })
  getList()
}

const handleSelectionChange = (selection: any[]) => {
  selectedIds.value = selection.map(item => item.id)
}

const handleAdd = () => {
  ElMessage.info('新增用户功能待实现')
}

const handleBatchEnable = async () => {
  try {
    await ElMessageBox.confirm('确定要批量启用选中的用户吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    ElMessage.success('批量启用成功')
    getList()
  } catch (error) {
    // 用户取消
  }
}

const handleBatchDisable = async () => {
  try {
    await ElMessageBox.confirm('确定要批量禁用选中的用户吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    ElMessage.success('批量禁用成功')
    getList()
  } catch (error) {
    // 用户取消
  }
}

const handleBatchDelete = async () => {
  try {
    await ElMessageBox.confirm('确定要批量删除选中的用户吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    })
    
    await methods.delList(selectedIds.value)
    ElMessage.success('批量删除成功')
    getList()
  } catch (error) {
    // 用户取消
  }
}

const handleDetail = (row: any) => {
  router.push(`/carbon-account-admin/enterprise-account/user-detail/${row.id}`)
}

const handleEdit = (row: any) => {
  ElMessage.info('编辑用户功能待实现')
}

const handleToggleStatus = async (row: any) => {
  const action = row.status === '1' ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(`确定要${action}用户"${row.userName}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 模拟API调用
    row.status = row.status === '1' ? '2' : '1'
    ElMessage.success(`${action}成功`)
  } catch (error) {
    // 用户取消
  }
}

const handleDelete = async (row: any) => {
  try {
    await ElMessageBox.confirm(`确定要删除用户"${row.userName}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    })
    
    await methods.delList([row.id])
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    // 用户取消
  }
}

onMounted(() => {
  getList()
})
</script>

<style lang="less" scoped>
.user-list {
  .search-area {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
  }
  
  .action-area {
    display: flex;
    gap: 10px;
  }
}
</style>
