<template>
  <div class="user-detail">
    <ContentWrap>
      <!-- 页面头部 -->
      <div class="page-header mb-20px">
        <ElButton @click="handleBack">
          <Icon icon="ep:arrow-left" class="mr-5px" />
          返回列表
        </ElButton>
        <div class="header-title">
          <h2>用户详情</h2>
          <ElTag :type="getUserStatusType(userInfo.status)" size="large">
            {{ getUserStatusText(userInfo.status) }}
          </ElTag>
        </div>
      </div>

      <!-- 基本信息 -->
      <ElCard class="mb-20px">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:user" class="mr-5px" />
            基本信息
          </div>
        </template>
        <div class="user-basic-info">
          <div class="avatar-section">
            <ElAvatar :src="userInfo.avatar" :size="100">
              {{ userInfo.userName.charAt(0) }}
            </ElAvatar>
            <div class="user-name">{{ userInfo.userName }}</div>
            <div class="user-account">{{ userInfo.account }}</div>
          </div>
          <div class="info-section">
            <ElRow :gutter="20">
              <ElCol :span="12">
                <div class="info-item">
                  <label>用户ID：</label>
                  <span>{{ userInfo.id }}</span>
                </div>
              </ElCol>
              <ElCol :span="12">
                <div class="info-item">
                  <label>用户姓名：</label>
                  <span>{{ userInfo.userName }}</span>
                </div>
              </ElCol>
              <ElCol :span="12">
                <div class="info-item">
                  <label>登录账号：</label>
                  <span>{{ userInfo.account }}</span>
                </div>
              </ElCol>
              <ElCol :span="12">
                <div class="info-item">
                  <label>手机号码：</label>
                  <span>{{ userInfo.phone }}</span>
                </div>
              </ElCol>
              <ElCol :span="12">
                <div class="info-item">
                  <label>邮箱地址：</label>
                  <span>{{ userInfo.email }}</span>
                </div>
              </ElCol>
              <ElCol :span="12">
                <div class="info-item">
                  <label>性别：</label>
                  <span>{{ userInfo.gender === '1' ? '男' : '女' }}</span>
                </div>
              </ElCol>
              <ElCol :span="12">
                <div class="info-item">
                  <label>部门：</label>
                  <span>{{ userInfo.department }}</span>
                </div>
              </ElCol>
              <ElCol :span="12">
                <div class="info-item">
                  <label>职位：</label>
                  <span>{{ userInfo.position }}</span>
                </div>
              </ElCol>
            </ElRow>
          </div>
        </div>
      </ElCard>

      <!-- 企业信息 -->
      <ElCard class="mb-20px">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:office-building" class="mr-5px" />
            所属企业
          </div>
        </template>
        <ElRow :gutter="20">
          <ElCol :span="12">
            <div class="info-item">
              <label>企业名称：</label>
              <span>{{ userInfo.enterpriseName }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>统一社会信用代码：</label>
              <span>{{ userInfo.enterpriseCreditCode }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>企业类型：</label>
              <span>{{ userInfo.enterpriseType }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>所属行业：</label>
              <span>{{ userInfo.industry }}</span>
            </div>
          </ElCol>
        </ElRow>
      </ElCard>

      <!-- 角色权限 -->
      <ElCard class="mb-20px">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:key" class="mr-5px" />
            角色权限
          </div>
        </template>
        <div class="roles-section">
          <div class="roles-list">
            <ElTag
              v-for="role in userInfo.roles"
              :key="role.id"
              size="large"
              class="role-tag"
            >
              {{ role.name }}
            </ElTag>
          </div>
          <div class="permissions-list mt-20px">
            <h4>权限列表：</h4>
            <ElTree
              :data="userInfo.permissions"
              show-checkbox
              node-key="id"
              :default-checked-keys="userInfo.checkedPermissions"
              :props="{ children: 'children', label: 'name' }"
              disabled
            />
          </div>
        </div>
      </ElCard>

      <!-- 登录记录 -->
      <ElCard class="mb-20px">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:clock" class="mr-5px" />
            登录记录
          </div>
        </template>
        <ElTable :data="loginRecords" style="width: 100%">
          <ElTableColumn prop="loginTime" label="登录时间" width="180" />
          <ElTableColumn prop="loginIp" label="登录IP" width="150" />
          <ElTableColumn prop="loginLocation" label="登录地点" width="200" />
          <ElTableColumn prop="userAgent" label="设备信息" min-width="200" />
          <ElTableColumn prop="loginStatus" label="登录状态" width="100">
            <template #default="{ row }">
              <ElTag :type="row.loginStatus === '1' ? 'success' : 'danger'">
                {{ row.loginStatus === '1' ? '成功' : '失败' }}
              </ElTag>
            </template>
          </ElTableColumn>
        </ElTable>
      </ElCard>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <ElButton type="primary" @click="handleEdit">
          <Icon icon="ep:edit" class="mr-5px" />
          编辑用户
        </ElButton>
        <ElButton type="warning" @click="handleResetPassword">
          <Icon icon="ep:refresh" class="mr-5px" />
          重置密码
        </ElButton>
        <ElButton
          :type="userInfo.status === '1' ? 'warning' : 'success'"
          @click="handleToggleStatus"
        >
          <Icon :icon="userInfo.status === '1' ? 'ep:lock' : 'ep:unlock'" class="mr-5px" />
          {{ userInfo.status === '1' ? '禁用用户' : '启用用户' }}
        </ElButton>
        <ElButton type="danger" @click="handleDelete">
          <Icon icon="ep:delete" class="mr-5px" />
          删除用户
        </ElButton>
      </div>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'

const router = useRouter()
const route = useRoute()

// 用户信息
const userInfo = ref({
  id: '',
  userName: '',
  account: '',
  phone: '',
  email: '',
  gender: '1',
  department: '',
  position: '',
  avatar: '',
  enterpriseName: '',
  enterpriseCreditCode: '',
  enterpriseType: '',
  industry: '',
  status: '1',
  roles: [] as any[],
  permissions: [] as any[],
  checkedPermissions: [] as string[]
})

// 登录记录
const loginRecords = ref([
  {
    loginTime: '2024-07-28 16:45:00',
    loginIp: '*************',
    loginLocation: '北京市朝阳区',
    userAgent: 'Chrome ********* / Windows 10',
    loginStatus: '1'
  },
  {
    loginTime: '2024-07-27 09:30:00',
    loginIp: '*************',
    loginLocation: '北京市朝阳区',
    userAgent: 'Chrome ********* / Windows 10',
    loginStatus: '1'
  },
  {
    loginTime: '2024-07-26 14:20:00',
    loginIp: '*************',
    loginLocation: '北京市海淀区',
    userAgent: 'Firefox 127.0 / Windows 10',
    loginStatus: '0'
  }
])

// 状态相关方法
const getUserStatusType = (status: string) => {
  const statusMap = {
    '1': 'success',
    '2': 'danger'
  }
  return statusMap[status] || 'info'
}

const getUserStatusText = (status: string) => {
  const statusMap = {
    '1': '正常',
    '2': '禁用'
  }
  return statusMap[status] || '未知'
}

// 获取用户详情
const getUserDetail = async () => {
  const id = route.params.id as string
  // 模拟API调用
  setTimeout(() => {
    userInfo.value = {
      id: id,
      userName: '张三',
      account: 'zhangsan',
      phone: '***********',
      email: '<EMAIL>',
      gender: '1',
      department: '技术部',
      position: '高级工程师',
      avatar: '',
      enterpriseName: '绿色科技有限公司',
      enterpriseCreditCode: '91110000123456789X',
      enterpriseType: '有限责任公司',
      industry: '环保技术',
      status: '1',
      roles: [
        { id: '1', name: '管理员' },
        { id: '2', name: '数据分析员' }
      ],
      permissions: [
        {
          id: '1',
          name: '系统管理',
          children: [
            { id: '11', name: '用户管理' },
            { id: '12', name: '角色管理' },
            { id: '13', name: '权限管理' }
          ]
        },
        {
          id: '2',
          name: '数据管理',
          children: [
            { id: '21', name: '数据查看' },
            { id: '22', name: '数据编辑' },
            { id: '23', name: '数据导出' }
          ]
        }
      ],
      checkedPermissions: ['11', '12', '21', '22', '23']
    }
  }, 500)
}

// 事件处理方法
const handleBack = () => {
  router.back()
}

const handleEdit = () => {
  ElMessage.info('编辑用户功能待实现')
}

const handleResetPassword = async () => {
  try {
    await ElMessageBox.confirm('确定要重置该用户的密码吗？', '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    ElMessage.success('密码重置成功，新密码已发送至用户邮箱')
  } catch (error) {
    // 用户取消
  }
}

const handleToggleStatus = async () => {
  const action = userInfo.value.status === '1' ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(`确定要${action}用户"${userInfo.value.userName}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 模拟API调用
    userInfo.value.status = userInfo.value.status === '1' ? '2' : '1'
    ElMessage.success(`${action}成功`)
  } catch (error) {
    // 用户取消
  }
}

const handleDelete = async () => {
  try {
    await ElMessageBox.confirm(`确定要删除用户"${userInfo.value.userName}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    })
    
    ElMessage.success('删除成功')
    router.back()
  } catch (error) {
    // 用户取消
  }
}

onMounted(() => {
  getUserDetail()
})
</script>

<style lang="less" scoped>
.user-detail {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .header-title {
      display: flex;
      align-items: center;
      gap: 15px;
      
      h2 {
        margin: 0;
        color: #303133;
      }
    }
  }
  
  .card-header {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #303133;
  }
  
  .user-basic-info {
    display: flex;
    gap: 30px;
    
    .avatar-section {
      text-align: center;
      
      .user-name {
        margin-top: 10px;
        font-size: 18px;
        font-weight: 600;
        color: #303133;
      }
      
      .user-account {
        margin-top: 5px;
        color: #909399;
      }
    }
    
    .info-section {
      flex: 1;
    }
  }
  
  .info-item {
    margin-bottom: 15px;
    
    label {
      font-weight: 600;
      color: #606266;
      margin-right: 10px;
    }
    
    span {
      color: #303133;
    }
  }
  
  .roles-section {
    .roles-list {
      .role-tag {
        margin-right: 10px;
        margin-bottom: 10px;
      }
    }
    
    h4 {
      color: #303133;
      margin-bottom: 15px;
    }
  }
  
  .action-buttons {
    text-align: center;
    
    .el-button {
      margin: 0 10px;
    }
  }
}
</style>
