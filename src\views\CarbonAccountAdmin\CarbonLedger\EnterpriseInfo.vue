<template>
  <div class="enterprise-info">
    <ContentWrap>
      <!-- 页面头部 -->
      <div class="page-header mb-20px">
        <ElButton @click="handleBack">
          <Icon icon="ep:arrow-left" class="mr-5px" />
          返回搜索
        </ElButton>
        <div class="header-title">
          <h2>{{ enterpriseInfo.enterpriseName }}</h2>
          <ElTag :type="getStatusType(enterpriseInfo.status)" size="large">
            {{ getStatusText(enterpriseInfo.status) }}
          </ElTag>
        </div>
      </div>

      <!-- 企业概览 -->
      <div class="enterprise-overview mb-20px">
        <ElRow :gutter="20">
          <ElCol :span="6">
            <ElCard class="overview-card emissions">
              <div class="overview-content">
                <div class="overview-icon">
                  <Icon icon="ep:warning" size="30" />
                </div>
                <div class="overview-info">
                  <div class="overview-number">{{ enterpriseInfo.totalEmissions }}</div>
                  <div class="overview-label">总碳排放量(吨)</div>
                </div>
              </div>
            </ElCard>
          </ElCol>
          <ElCol :span="6">
            <ElCard class="overview-card reduction">
              <div class="overview-content">
                <div class="overview-icon">
                  <Icon icon="ep:trend-charts" size="30" />
                </div>
                <div class="overview-info">
                  <div class="overview-number">{{ enterpriseInfo.reductionRate }}%</div>
                  <div class="overview-label">减排率</div>
                </div>
              </div>
            </ElCard>
          </ElCol>
          <ElCol :span="6">
            <ElCard class="overview-card energy">
              <div class="overview-content">
                <div class="overview-icon">
                  <Icon icon="ep:lightning" size="30" />
                </div>
                <div class="overview-info">
                  <div class="overview-number">{{ enterpriseInfo.energyConsumption }}</div>
                  <div class="overview-label">能耗(万千瓦时)</div>
                </div>
              </div>
            </ElCard>
          </ElCol>
          <ElCol :span="6">
            <ElCard class="overview-card score">
              <div class="overview-content">
                <div class="overview-icon">
                  <Icon icon="ep:medal" size="30" />
                </div>
                <div class="overview-info">
                  <div class="overview-number">{{ enterpriseInfo.carbonScore }}</div>
                  <div class="overview-label">碳积分</div>
                </div>
              </div>
            </ElCard>
          </ElCol>
        </ElRow>
      </div>

      <!-- 基本信息 -->
      <ElCard class="mb-20px">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:office-building" class="mr-5px" />
            企业基本信息
          </div>
        </template>
        <ElRow :gutter="20">
          <ElCol :span="12">
            <div class="info-item">
              <label>企业名称：</label>
              <span>{{ enterpriseInfo.enterpriseName }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>统一社会信用代码：</label>
              <span>{{ enterpriseInfo.creditCode }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>法定代表人：</label>
              <span>{{ enterpriseInfo.legalPerson }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>注册资本：</label>
              <span>{{ enterpriseInfo.registeredCapital }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>所属行业：</label>
              <span>{{ enterpriseInfo.industry }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>企业规模：</label>
              <span>{{ enterpriseInfo.scale }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>成立日期：</label>
              <span>{{ enterpriseInfo.establishmentDate }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>注册时间：</label>
              <span>{{ enterpriseInfo.registrationTime }}</span>
            </div>
          </ElCol>
          <ElCol :span="24">
            <div class="info-item">
              <label>注册地址：</label>
              <span>{{ enterpriseInfo.registeredAddress }}</span>
            </div>
          </ElCol>
        </ElRow>
      </ElCard>

      <!-- 碳排放数据 -->
      <ElCard class="mb-20px">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:data-analysis" class="mr-5px" />
            碳排放数据
            <div class="header-actions">
              <ElButton type="primary" @click="handleExportData">
                <Icon icon="ep:download" class="mr-5px" />
                导出数据
              </ElButton>
            </div>
          </div>
        </template>
        <ElTable :data="carbonData" style="width: 100%">
          <ElTableColumn prop="year" label="年份" width="100" />
          <ElTableColumn prop="scope1" label="范围1排放(吨)" width="150" />
          <ElTableColumn prop="scope2" label="范围2排放(吨)" width="150" />
          <ElTableColumn prop="scope3" label="范围3排放(吨)" width="150" />
          <ElTableColumn prop="total" label="总排放量(吨)" width="150" />
          <ElTableColumn prop="reductionRate" label="减排率(%)" width="120" />
          <ElTableColumn prop="updateTime" label="更新时间" width="150" />
          <ElTableColumn label="操作" width="120">
            <template #default="{ row }">
              <ElButton type="primary" link @click="handleViewDetail(row)">
                查看详情
              </ElButton>
            </template>
          </ElTableColumn>
        </ElTable>
      </ElCard>

      <!-- 减排措施 -->
      <ElCard class="mb-20px">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:tools" class="mr-5px" />
            减排措施
          </div>
        </template>
        <div class="measures-list">
          <div
            v-for="measure in reductionMeasures"
            :key="measure.id"
            class="measure-item"
          >
            <div class="measure-header">
              <h4>{{ measure.title }}</h4>
              <ElTag :type="getMeasureStatusType(measure.status)">
                {{ getMeasureStatusText(measure.status) }}
              </ElTag>
            </div>
            <div class="measure-content">
              <p>{{ measure.description }}</p>
              <div class="measure-stats">
                <span class="stat-item">
                  <label>预计减排量：</label>
                  <span>{{ measure.expectedReduction }}吨</span>
                </span>
                <span class="stat-item">
                  <label>实施时间：</label>
                  <span>{{ measure.implementTime }}</span>
                </span>
                <span class="stat-item">
                  <label>投资金额：</label>
                  <span>{{ measure.investment }}万元</span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </ElCard>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <ElButton type="primary" @click="handleGenerateReport">
          <Icon icon="ep:document" class="mr-5px" />
          生成报告
        </ElButton>
        <ElButton type="success" @click="handleExportAll">
          <Icon icon="ep:download" class="mr-5px" />
          导出全部数据
        </ElButton>
        <ElButton type="warning" @click="handleCompare">
          <Icon icon="ep:data-analysis" class="mr-5px" />
          对比分析
        </ElButton>
        <ElButton type="info" @click="handleViewHistory">
          <Icon icon="ep:clock" class="mr-5px" />
          查看历史
        </ElButton>
      </div>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'

const router = useRouter()
const route = useRoute()

// 企业信息
const enterpriseInfo = ref({
  id: '',
  enterpriseName: '',
  creditCode: '',
  legalPerson: '',
  registeredCapital: '',
  industry: '',
  scale: '',
  establishmentDate: '',
  registrationTime: '',
  registeredAddress: '',
  status: '1',
  totalEmissions: 0,
  reductionRate: 0,
  energyConsumption: 0,
  carbonScore: 0
})

// 碳排放数据
const carbonData = ref([
  {
    year: '2024',
    scope1: 1234.56,
    scope2: 2345.67,
    scope3: 3456.78,
    total: 7036.01,
    reductionRate: 15.2,
    updateTime: '2024-07-28'
  },
  {
    year: '2023',
    scope1: 1456.78,
    scope2: 2567.89,
    scope3: 3678.90,
    total: 7703.57,
    reductionRate: 12.8,
    updateTime: '2024-01-15'
  },
  {
    year: '2022',
    scope1: 1678.90,
    scope2: 2789.01,
    scope3: 3890.12,
    total: 8358.03,
    reductionRate: 8.5,
    updateTime: '2023-01-15'
  }
])

// 减排措施
const reductionMeasures = ref([
  {
    id: '1',
    title: '节能设备改造',
    description: '更换高效节能设备，优化生产工艺流程，提高能源利用效率。',
    expectedReduction: 500,
    implementTime: '2024-03-01',
    investment: 200,
    status: '2'
  },
  {
    id: '2',
    title: '可再生能源利用',
    description: '安装太阳能发电系统，减少对传统能源的依赖。',
    expectedReduction: 800,
    implementTime: '2024-06-01',
    investment: 500,
    status: '1'
  },
  {
    id: '3',
    title: '废料回收利用',
    description: '建立完善的废料回收体系，提高资源循环利用率。',
    expectedReduction: 300,
    implementTime: '2024-09-01',
    investment: 150,
    status: '0'
  }
])

// 状态相关方法
const getStatusType = (status: string) => {
  const statusMap = {
    '1': 'success',
    '2': 'warning',
    '3': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    '1': '正常',
    '2': '冻结',
    '3': '注销'
  }
  return statusMap[status] || '未知'
}

const getMeasureStatusType = (status: string) => {
  const statusMap = {
    '0': 'info',
    '1': 'warning',
    '2': 'success'
  }
  return statusMap[status] || 'info'
}

const getMeasureStatusText = (status: string) => {
  const statusMap = {
    '0': '计划中',
    '1': '实施中',
    '2': '已完成'
  }
  return statusMap[status] || '未知'
}

// 获取企业信息
const getEnterpriseInfo = async () => {
  const id = route.params.id as string
  // 模拟API调用
  setTimeout(() => {
    enterpriseInfo.value = {
      id: id,
      enterpriseName: '绿色科技有限公司',
      creditCode: '91110000123456789X',
      legalPerson: '张三',
      registeredCapital: '1000万元',
      industry: '新能源',
      scale: '中型企业',
      establishmentDate: '2020-05-15',
      registrationTime: '2024-01-15',
      registeredAddress: '北京市朝阳区科技园区创新大厦A座1001室',
      status: '1',
      totalEmissions: 7036.01,
      reductionRate: 15.2,
      energyConsumption: 1250.5,
      carbonScore: 8520
    }
  }, 500)
}

// 事件处理方法
const handleBack = () => {
  router.back()
}

const handleExportData = () => {
  ElMessage.info('导出数据功能待实现')
}

const handleViewDetail = (row: any) => {
  ElMessage.info(`查看${row.year}年详细数据`)
}

const handleGenerateReport = () => {
  ElMessage.info('生成报告功能待实现')
}

const handleExportAll = () => {
  ElMessage.info('导出全部数据功能待实现')
}

const handleCompare = () => {
  ElMessage.info('对比分析功能待实现')
}

const handleViewHistory = () => {
  ElMessage.info('查看历史功能待实现')
}

onMounted(() => {
  getEnterpriseInfo()
})
</script>

<style lang="less" scoped>
.enterprise-info {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .header-title {
      display: flex;
      align-items: center;
      gap: 15px;
      
      h2 {
        margin: 0;
        color: #303133;
      }
    }
  }
  
  .enterprise-overview {
    .overview-card {
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
      
      &.emissions {
        border-left: 4px solid #f56c6c;
      }
      
      &.reduction {
        border-left: 4px solid #67c23a;
      }
      
      &.energy {
        border-left: 4px solid #e6a23c;
      }
      
      &.score {
        border-left: 4px solid #409eff;
      }
      
      .overview-content {
        display: flex;
        align-items: center;
        gap: 15px;
        
        .overview-icon {
          color: #409eff;
        }
        
        .overview-info {
          .overview-number {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 5px;
          }
          
          .overview-label {
            font-size: 14px;
            color: #909399;
          }
        }
      }
    }
  }
  
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 600;
    color: #303133;
    
    .header-actions {
      display: flex;
      gap: 10px;
    }
  }
  
  .info-item {
    margin-bottom: 15px;
    
    label {
      font-weight: 600;
      color: #606266;
      margin-right: 10px;
    }
    
    span {
      color: #303133;
    }
  }
  
  .measures-list {
    .measure-item {
      border: 1px solid #ebeef5;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 15px;
      
      .measure-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        
        h4 {
          margin: 0;
          color: #303133;
        }
      }
      
      .measure-content {
        p {
          color: #606266;
          margin-bottom: 15px;
        }
        
        .measure-stats {
          display: flex;
          gap: 30px;
          
          .stat-item {
            label {
              font-weight: 600;
              color: #606266;
              margin-right: 8px;
            }
            
            span {
              color: #303133;
            }
          }
        }
      }
    }
  }
  
  .action-buttons {
    text-align: center;
    
    .el-button {
      margin: 0 10px;
    }
  }
}
</style>
