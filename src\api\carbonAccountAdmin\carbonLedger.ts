import request from '@/config/axios'

// 碳账本概览数据
export interface CarbonLedgerOverview {
  totalEnterprises: number
  activeEnterprises: number
  totalCarbonData: number
  totalEmissions: number
}

// 企业碳信息
export interface EnterpriseCarbonInfo {
  id: string
  enterpriseName: string
  creditCode: string
  industry: string
  scale: string
  registrationTime: string
  status: string
  totalEmissions: number
  yearEmissions: number
  reductionRate: number
  lastUpdateTime: string
  legalPerson: string
  registeredCapital: string
  establishmentDate: string
  registeredAddress: string
  energyConsumption: number
  carbonScore: number
}

// 碳排放数据
export interface CarbonEmissionData {
  year: string
  scope1: number
  scope2: number
  scope3: number
  total: number
  reductionRate: number
  updateTime: string
}

// 减排措施
export interface ReductionMeasure {
  id: string
  title: string
  description: string
  expectedReduction: number
  implementTime: string
  investment: number
  status: string
}

// 获取碳账本概览
export const getCarbonLedgerOverview = () => {
  return request.get({ url: '/carbon-account-admin/carbon-ledger/overview' })
}

// 获取最新注册企业
export const getRecentEnterprises = (params: any) => {
  return request.get({ url: '/carbon-account-admin/carbon-ledger/recent-enterprises', params })
}

// 搜索企业碳账本
export const searchEnterpriseCarbonLedger = (params: any) => {
  return request.get({ url: '/carbon-account-admin/carbon-ledger/search', params })
}

// 获取企业碳信息详情
export const getEnterpriseCarbonInfo = (id: string) => {
  return request.get({ url: `/carbon-account-admin/carbon-ledger/enterprise-info/${id}` })
}

// 获取企业碳排放数据
export const getEnterpriseCarbonData = (id: string, params: any) => {
  return request.get({ url: `/carbon-account-admin/carbon-ledger/enterprise-carbon-data/${id}`, params })
}

// 获取企业减排措施
export const getEnterpriseReductionMeasures = (id: string) => {
  return request.get({ url: `/carbon-account-admin/carbon-ledger/enterprise-reduction-measures/${id}` })
}

// 导出企业碳数据
export const exportEnterpriseCarbonData = (id: string, params: any) => {
  return request.get({ url: `/carbon-account-admin/carbon-ledger/export-carbon-data/${id}`, params })
}

// 生成企业碳报告
export const generateEnterpriseCarbonReport = (id: string, params: any) => {
  return request.post({ url: `/carbon-account-admin/carbon-ledger/generate-report/${id}`, data: params })
}

// Mock数据生成器
export const mockCarbonLedgerOverview = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = {
        totalEnterprises: 1256,
        activeEnterprises: 1089,
        totalCarbonData: 45678,
        totalEmissions: 123456.78
      }
      resolve(mockData)
    }, 300)
  })
}

export const mockRecentEnterprises = (params: any) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = [
        {
          id: '1',
          enterpriseName: '新能源科技有限公司',
          industry: '新能源',
          registrationTime: '2024-07-28',
          status: '1'
        },
        {
          id: '2',
          enterpriseName: '智能制造股份有限公司',
          industry: '制造业',
          registrationTime: '2024-07-27',
          status: '1'
        },
        {
          id: '3',
          enterpriseName: '绿色建筑工程有限公司',
          industry: '建筑业',
          registrationTime: '2024-07-26',
          status: '0'
        },
        {
          id: '4',
          enterpriseName: '环保科技发展有限公司',
          industry: '环保',
          registrationTime: '2024-07-25',
          status: '1'
        },
        {
          id: '5',
          enterpriseName: '清洁能源投资有限公司',
          industry: '投资',
          registrationTime: '2024-07-24',
          status: '1'
        }
      ]
      resolve(mockData)
    }, 300)
  })
}

export const mockSearchEnterpriseCarbonLedger = (params: any) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = {
        list: [
          {
            id: '1',
            enterpriseName: '绿色科技有限公司',
            creditCode: '91110000123456789X',
            industry: '新能源',
            scale: '中型企业',
            registrationTime: '2024-01-15',
            status: '1',
            totalEmissions: 2456.78,
            yearEmissions: 1234.56,
            reductionRate: 15.2,
            lastUpdateTime: '2024-07-28'
          },
          {
            id: '2',
            enterpriseName: '环保能源股份有限公司',
            creditCode: '91110000987654321Y',
            industry: '环保',
            scale: '大型企业',
            registrationTime: '2024-02-20',
            status: '1',
            totalEmissions: 5678.90,
            yearEmissions: 2890.45,
            reductionRate: 12.8,
            lastUpdateTime: '2024-07-27'
          },
          {
            id: '3',
            enterpriseName: '智能制造有限公司',
            creditCode: '91110000456789123Z',
            industry: '制造业',
            scale: '中型企业',
            registrationTime: '2024-03-10',
            status: '1',
            totalEmissions: 3456.78,
            yearEmissions: 1789.23,
            reductionRate: 18.5,
            lastUpdateTime: '2024-07-26'
          }
        ],
        total: 3
      }
      resolve(mockData)
    }, 800)
  })
}

export const mockEnterpriseCarbonInfo = (id: string) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = {
        id: id,
        enterpriseName: '绿色科技有限公司',
        creditCode: '91110000123456789X',
        legalPerson: '张三',
        registeredCapital: '1000万元',
        industry: '新能源',
        scale: '中型企业',
        establishmentDate: '2020-05-15',
        registrationTime: '2024-01-15',
        registeredAddress: '北京市朝阳区科技园区创新大厦A座1001室',
        status: '1',
        totalEmissions: 7036.01,
        reductionRate: 15.2,
        energyConsumption: 1250.5,
        carbonScore: 8520
      }
      resolve(mockData)
    }, 500)
  })
}

export const mockEnterpriseCarbonData = (id: string, params: any) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = [
        {
          year: '2024',
          scope1: 1234.56,
          scope2: 2345.67,
          scope3: 3456.78,
          total: 7036.01,
          reductionRate: 15.2,
          updateTime: '2024-07-28'
        },
        {
          year: '2023',
          scope1: 1456.78,
          scope2: 2567.89,
          scope3: 3678.90,
          total: 7703.57,
          reductionRate: 12.8,
          updateTime: '2024-01-15'
        },
        {
          year: '2022',
          scope1: 1678.90,
          scope2: 2789.01,
          scope3: 3890.12,
          total: 8358.03,
          reductionRate: 8.5,
          updateTime: '2023-01-15'
        },
        {
          year: '2021',
          scope1: 1890.12,
          scope2: 2901.23,
          scope3: 4012.34,
          total: 8803.69,
          reductionRate: 5.2,
          updateTime: '2022-01-15'
        }
      ]
      resolve(mockData)
    }, 400)
  })
}

export const mockEnterpriseReductionMeasures = (id: string) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = [
        {
          id: '1',
          title: '节能设备改造',
          description: '更换高效节能设备，优化生产工艺流程，提高能源利用效率。',
          expectedReduction: 500,
          implementTime: '2024-03-01',
          investment: 200,
          status: '2'
        },
        {
          id: '2',
          title: '可再生能源利用',
          description: '安装太阳能发电系统，减少对传统能源的依赖。',
          expectedReduction: 800,
          implementTime: '2024-06-01',
          investment: 500,
          status: '1'
        },
        {
          id: '3',
          title: '废料回收利用',
          description: '建立完善的废料回收体系，提高资源循环利用率。',
          expectedReduction: 300,
          implementTime: '2024-09-01',
          investment: 150,
          status: '0'
        },
        {
          id: '4',
          title: '智能化管理系统',
          description: '引入智能化管理系统，实时监控能耗和排放数据。',
          expectedReduction: 400,
          implementTime: '2024-12-01',
          investment: 300,
          status: '0'
        }
      ]
      resolve(mockData)
    }, 400)
  })
}

// 地区选项数据
export const mockRegionOptions = () => {
  return [
    {
      value: 'beijing',
      label: '北京市',
      children: [
        { value: 'chaoyang', label: '朝阳区' },
        { value: 'haidian', label: '海淀区' },
        { value: 'dongcheng', label: '东城区' },
        { value: 'xicheng', label: '西城区' },
        { value: 'fengtai', label: '丰台区' },
        { value: 'shijingshan', label: '石景山区' }
      ]
    },
    {
      value: 'shanghai',
      label: '上海市',
      children: [
        { value: 'huangpu', label: '黄浦区' },
        { value: 'xuhui', label: '徐汇区' },
        { value: 'changning', label: '长宁区' },
        { value: 'jingan', label: '静安区' },
        { value: 'putuo', label: '普陀区' },
        { value: 'hongkou', label: '虹口区' }
      ]
    },
    {
      value: 'guangzhou',
      label: '广州市',
      children: [
        { value: 'tianhe', label: '天河区' },
        { value: 'yuexiu', label: '越秀区' },
        { value: 'liwan', label: '荔湾区' },
        { value: 'haizhu', label: '海珠区' },
        { value: 'baiyun', label: '白云区' },
        { value: 'panyu', label: '番禺区' }
      ]
    },
    {
      value: 'shenzhen',
      label: '深圳市',
      children: [
        { value: 'futian', label: '福田区' },
        { value: 'luohu', label: '罗湖区' },
        { value: 'nanshan', label: '南山区' },
        { value: 'yantian', label: '盐田区' },
        { value: 'baoan', label: '宝安区' },
        { value: 'longgang', label: '龙岗区' }
      ]
    }
  ]
}
