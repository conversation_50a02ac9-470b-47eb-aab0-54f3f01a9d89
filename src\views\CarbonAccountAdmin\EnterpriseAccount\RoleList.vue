<template>
  <div class="role-list">
    <ContentWrap>
      <!-- 搜索区域 -->
      <div class="search-area mb-20px">
        <ElForm :model="searchForm" inline>
          <ElFormItem label="角色名称">
            <ElInput
              v-model="searchForm.roleName"
              placeholder="请输入角色名称"
              clearable
              style="width: 200px"
            />
          </ElFormItem>
          <ElFormItem label="角色状态">
            <ElSelect
              v-model="searchForm.status"
              placeholder="请选择状态"
              clearable
              style="width: 150px"
            >
              <ElOption label="全部" value="" />
              <ElOption label="启用" value="1" />
              <ElOption label="禁用" value="2" />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="创建时间">
            <ElDatePicker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </ElFormItem>
          <ElFormItem>
            <ElButton type="primary" @click="handleSearch">
              <Icon icon="ep:search" class="mr-5px" />
              搜索
            </ElButton>
            <ElButton @click="handleReset">
              <Icon icon="ep:refresh" class="mr-5px" />
              重置
            </ElButton>
          </ElFormItem>
        </ElForm>
      </div>

      <!-- 操作按钮区域 -->
      <div class="action-area mb-20px">
        <ElButton type="primary" @click="handleAdd">
          <Icon icon="ep:plus" class="mr-5px" />
          新增角色
        </ElButton>
        <ElButton type="success" @click="handleExport">
          <Icon icon="ep:download" class="mr-5px" />
          导出数据
        </ElButton>
      </div>

      <!-- 表格区域 -->
      <Table
        v-model:pageSize="tableObject.pageSize"
        v-model:currentPage="tableObject.currentPage"
        :columns="columns"
        :data="tableObject.tableList"
        :loading="tableObject.loading"
        :pagination="{
          total: tableObject.total
        }"
        @register="register"
      >
        <template #status="{ row }">
          <ElTag :type="getRoleStatusType(row.status)">
            {{ getRoleStatusText(row.status) }}
          </ElTag>
        </template>
        <template #permissions="{ row }">
          <ElTooltip
            :content="row.permissions.map(p => p.name).join('、')"
            placement="top"
          >
            <ElTag size="small">
              {{ row.permissions.length }}个权限
            </ElTag>
          </ElTooltip>
        </template>
        <template #action="{ row }">
          <ElButton type="primary" link @click="handleDetail(row)">
            <Icon icon="ep:view" class="mr-5px" />
            查看详情
          </ElButton>
          <ElButton type="warning" link @click="handleEdit(row)">
            <Icon icon="ep:edit" class="mr-5px" />
            编辑
          </ElButton>
          <ElButton
            :type="row.status === '1' ? 'warning' : 'success'"
            link
            @click="handleToggleStatus(row)"
          >
            <Icon :icon="row.status === '1' ? 'ep:lock' : 'ep:unlock'" class="mr-5px" />
            {{ row.status === '1' ? '禁用' : '启用' }}
          </ElButton>
          <ElButton type="danger" link @click="handleDelete(row)" v-if="!row.isSystem">
            <Icon icon="ep:delete" class="mr-5px" />
            删除
          </ElButton>
        </template>
      </Table>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElMessageBox } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Table } from '@/components/Table'
import { useTable } from '@/hooks/web/useTable'
import { Icon } from '@/components/Icon'

const router = useRouter()

// 搜索表单
const searchForm = reactive({
  roleName: '',
  status: '',
  dateRange: []
})

// 表格配置
const columns = [
  {
    field: 'index',
    label: '序号',
    type: 'index',
    width: 80
  },
  {
    field: 'roleName',
    label: '角色名称',
    width: 150
  },
  {
    field: 'roleCode',
    label: '角色编码',
    width: 150
  },
  {
    field: 'description',
    label: '角色描述',
    minWidth: 200
  },
  {
    field: 'permissions',
    label: '权限数量',
    width: 120,
    slots: {
      default: 'permissions'
    }
  },
  {
    field: 'userCount',
    label: '用户数量',
    width: 100
  },
  {
    field: 'createTime',
    label: '创建时间',
    width: 150
  },
  {
    field: 'status',
    label: '状态',
    width: 80,
    slots: {
      default: 'status'
    }
  },
  {
    field: 'action',
    label: '操作',
    width: 220,
    slots: {
      default: 'action'
    }
  }
]

// 表格钩子
const { register, tableObject, methods } = useTable({
  getListApi: getRoleList,
  delListApi: deleteRole
})

const { getList } = methods

// 模拟API
async function getRoleList(params: any) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = {
        list: [
          {
            id: '1',
            roleName: '超级管理员',
            roleCode: 'super_admin',
            description: '系统超级管理员，拥有所有权限',
            permissions: [
              { id: '1', name: '用户管理' },
              { id: '2', name: '角色管理' },
              { id: '3', name: '权限管理' },
              { id: '4', name: '系统设置' }
            ],
            userCount: 1,
            createTime: '2024-01-01',
            status: '1',
            isSystem: true
          },
          {
            id: '2',
            roleName: '企业管理员',
            roleCode: 'enterprise_admin',
            description: '企业管理员，负责企业内部用户和数据管理',
            permissions: [
              { id: '1', name: '用户管理' },
              { id: '5', name: '数据管理' },
              { id: '6', name: '报表查看' }
            ],
            userCount: 5,
            createTime: '2024-01-15',
            status: '1',
            isSystem: false
          },
          {
            id: '3',
            roleName: '数据操作员',
            roleCode: 'data_operator',
            description: '数据操作员，负责数据录入和基础操作',
            permissions: [
              { id: '5', name: '数据管理' },
              { id: '7', name: '数据录入' }
            ],
            userCount: 12,
            createTime: '2024-02-01',
            status: '1',
            isSystem: false
          },
          {
            id: '4',
            roleName: '数据查看员',
            roleCode: 'data_viewer',
            description: '数据查看员，只能查看相关数据',
            permissions: [
              { id: '6', name: '报表查看' },
              { id: '8', name: '数据查看' }
            ],
            userCount: 8,
            createTime: '2024-02-15',
            status: '2',
            isSystem: false
          }
        ],
        total: 4
      }
      resolve(mockData)
    }, 500)
  })
}

async function deleteRole(ids: string[]) {
  return new Promise((resolve) => {
    setTimeout(() => {
      resolve(true)
    }, 500)
  })
}

// 状态相关方法
const getRoleStatusType = (status: string) => {
  const statusMap = {
    '1': 'success',
    '2': 'danger'
  }
  return statusMap[status] || 'info'
}

const getRoleStatusText = (status: string) => {
  const statusMap = {
    '1': '启用',
    '2': '禁用'
  }
  return statusMap[status] || '未知'
}

// 事件处理方法
const handleSearch = () => {
  getList()
}

const handleReset = () => {
  Object.assign(searchForm, {
    roleName: '',
    status: '',
    dateRange: []
  })
  getList()
}

const handleAdd = () => {
  ElMessage.info('新增角色功能待实现')
}

const handleExport = () => {
  ElMessage.info('导出功能待实现')
}

const handleDetail = (row: any) => {
  router.push(`/carbon-account-admin/enterprise-account/role-detail/${row.id}`)
}

const handleEdit = (row: any) => {
  ElMessage.info('编辑角色功能待实现')
}

const handleToggleStatus = async (row: any) => {
  const action = row.status === '1' ? '禁用' : '启用'
  try {
    await ElMessageBox.confirm(`确定要${action}角色"${row.roleName}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    })
    
    // 模拟API调用
    row.status = row.status === '1' ? '2' : '1'
    ElMessage.success(`${action}成功`)
  } catch (error) {
    // 用户取消
  }
}

const handleDelete = async (row: any) => {
  if (row.userCount > 0) {
    ElMessage.warning('该角色下还有用户，无法删除')
    return
  }
  
  try {
    await ElMessageBox.confirm(`确定要删除角色"${row.roleName}"吗？`, '提示', {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'error'
    })
    
    await methods.delList([row.id])
    ElMessage.success('删除成功')
    getList()
  } catch (error) {
    // 用户取消
  }
}

onMounted(() => {
  getList()
})
</script>

<style lang="less" scoped>
.role-list {
  .search-area {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
  }
  
  .action-area {
    display: flex;
    gap: 10px;
  }
}
</style>
