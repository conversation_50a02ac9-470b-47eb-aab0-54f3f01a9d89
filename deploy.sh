#!/bin/bash

# 碳账户管理系统 Docker 部署脚本
# 使用方法: ./deploy.sh [command] [options]

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_NAME="carbon-account"
COMPOSE_FILE="docker-compose.yml"
ENV_FILE=".env.docker"

# 日志函数
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# 检查 Docker 和 Docker Compose
check_requirements() {
    log_info "检查系统要求..."
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker 未安装，请先安装 Docker"
        exit 1
    fi
    
    if ! command -v docker-compose &> /dev/null && ! docker compose version &> /dev/null; then
        log_error "Docker Compose 未安装，请先安装 Docker Compose"
        exit 1
    fi
    
    log_success "系统要求检查通过"
}

# 创建环境变量文件
create_env_file() {
    if [ ! -f "$ENV_FILE" ]; then
        log_info "创建环境变量文件..."
        cat > "$ENV_FILE" << EOF
# 数据库配置
POSTGRES_DB=carbon_account
POSTGRES_USER=carbon_user
POSTGRES_PASSWORD=carbon_password_$(date +%s)

# Redis 配置
REDIS_PASSWORD=redis_password_$(date +%s)

# 应用配置
NODE_ENV=production
TZ=Asia/Shanghai

# 监控配置
GRAFANA_ADMIN_PASSWORD=admin123

# 网络配置
FRONTEND_PORT=80
BACKEND_PORT=3000
POSTGRES_PORT=5432
REDIS_PORT=6379
EOF
        log_success "环境变量文件创建完成: $ENV_FILE"
    else
        log_info "环境变量文件已存在: $ENV_FILE"
    fi
}

# 构建镜像
build_images() {
    log_info "构建 Docker 镜像..."
    docker-compose -f "$COMPOSE_FILE" build --no-cache
    log_success "镜像构建完成"
}

# 启动服务
start_services() {
    local profile=${1:-""}
    log_info "启动服务..."
    
    if [ -n "$profile" ]; then
        docker-compose -f "$COMPOSE_FILE" --profile "$profile" up -d
    else
        docker-compose -f "$COMPOSE_FILE" up -d carbon-account-frontend postgres redis
    fi
    
    log_success "服务启动完成"
}

# 停止服务
stop_services() {
    log_info "停止服务..."
    docker-compose -f "$COMPOSE_FILE" down
    log_success "服务已停止"
}

# 重启服务
restart_services() {
    log_info "重启服务..."
    stop_services
    start_services "$1"
    log_success "服务重启完成"
}

# 查看服务状态
show_status() {
    log_info "服务状态:"
    docker-compose -f "$COMPOSE_FILE" ps
    
    echo ""
    log_info "服务健康状态:"
    docker-compose -f "$COMPOSE_FILE" ps --format "table {{.Name}}\t{{.Status}}\t{{.Ports}}"
}

# 查看日志
show_logs() {
    local service=${1:-""}
    if [ -n "$service" ]; then
        log_info "查看 $service 服务日志:"
        docker-compose -f "$COMPOSE_FILE" logs -f "$service"
    else
        log_info "查看所有服务日志:"
        docker-compose -f "$COMPOSE_FILE" logs -f
    fi
}

# 清理资源
cleanup() {
    log_warning "清理 Docker 资源..."
    read -p "确定要清理所有相关资源吗？(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        docker-compose -f "$COMPOSE_FILE" down -v --remove-orphans
        docker system prune -f
        log_success "清理完成"
    else
        log_info "取消清理操作"
    fi
}

# 备份数据
backup_data() {
    local backup_dir="backups/$(date +%Y%m%d_%H%M%S)"
    log_info "备份数据到 $backup_dir..."
    
    mkdir -p "$backup_dir"
    
    # 备份数据库
    docker-compose -f "$COMPOSE_FILE" exec -T postgres pg_dump -U carbon_user carbon_account > "$backup_dir/database.sql"
    
    # 备份 Redis 数据
    docker-compose -f "$COMPOSE_FILE" exec -T redis redis-cli BGSAVE
    docker cp $(docker-compose -f "$COMPOSE_FILE" ps -q redis):/data/dump.rdb "$backup_dir/redis.rdb"
    
    log_success "数据备份完成: $backup_dir"
}

# 恢复数据
restore_data() {
    local backup_dir="$1"
    if [ -z "$backup_dir" ] || [ ! -d "$backup_dir" ]; then
        log_error "请指定有效的备份目录"
        exit 1
    fi
    
    log_warning "恢复数据从 $backup_dir..."
    read -p "确定要恢复数据吗？这将覆盖现有数据！(y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        # 恢复数据库
        if [ -f "$backup_dir/database.sql" ]; then
            docker-compose -f "$COMPOSE_FILE" exec -T postgres psql -U carbon_user -d carbon_account < "$backup_dir/database.sql"
        fi
        
        # 恢复 Redis 数据
        if [ -f "$backup_dir/redis.rdb" ]; then
            docker cp "$backup_dir/redis.rdb" $(docker-compose -f "$COMPOSE_FILE" ps -q redis):/data/dump.rdb
            docker-compose -f "$COMPOSE_FILE" restart redis
        fi
        
        log_success "数据恢复完成"
    else
        log_info "取消恢复操作"
    fi
}

# 显示帮助信息
show_help() {
    echo "碳账户管理系统 Docker 部署脚本"
    echo ""
    echo "使用方法:"
    echo "  $0 [command] [options]"
    echo ""
    echo "命令:"
    echo "  init                初始化项目（检查要求、创建环境文件）"
    echo "  build               构建 Docker 镜像"
    echo "  start [profile]     启动服务（可选 profile: monitoring, loadbalancer）"
    echo "  stop                停止服务"
    echo "  restart [profile]   重启服务"
    echo "  status              查看服务状态"
    echo "  logs [service]      查看日志（可选指定服务名）"
    echo "  backup              备份数据"
    echo "  restore <dir>       恢复数据"
    echo "  cleanup             清理 Docker 资源"
    echo "  help                显示帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 init                    # 初始化项目"
    echo "  $0 start                   # 启动基础服务"
    echo "  $0 start monitoring        # 启动包含监控的服务"
    echo "  $0 logs carbon-account-frontend  # 查看前端日志"
    echo "  $0 backup                  # 备份数据"
    echo "  $0 restore backups/20241129_143000  # 恢复数据"
}

# 主函数
main() {
    case "${1:-help}" in
        "init")
            check_requirements
            create_env_file
            ;;
        "build")
            check_requirements
            build_images
            ;;
        "start")
            check_requirements
            start_services "$2"
            show_status
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            check_requirements
            restart_services "$2"
            show_status
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs "$2"
            ;;
        "backup")
            backup_data
            ;;
        "restore")
            restore_data "$2"
            ;;
        "cleanup")
            cleanup
            ;;
        "help"|*)
            show_help
            ;;
    esac
}

# 执行主函数
main "$@"
