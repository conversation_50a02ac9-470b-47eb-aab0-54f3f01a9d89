<template>
  <div class="registration-detail">
    <ContentWrap>
      <!-- 页面头部 -->
      <div class="page-header mb-20px">
        <ElButton @click="handleBack">
          <Icon icon="ep:arrow-left" class="mr-5px" />
          返回列表
        </ElButton>
        <div class="header-title">
          <h2>企业注册申请详情</h2>
          <ElTag :type="getAuditStatusType(registrationInfo.auditStatus)" size="large">
            {{ getAuditStatusText(registrationInfo.auditStatus) }}
          </ElTag>
        </div>
      </div>

      <!-- 申请信息 -->
      <ElCard class="mb-20px">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:document" class="mr-5px" />
            申请信息
          </div>
        </template>
        <ElRow :gutter="20">
          <ElCol :span="12">
            <div class="info-item">
              <label>申请编号：</label>
              <span>{{ registrationInfo.applicationNumber }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>申请时间：</label>
              <span>{{ registrationInfo.applicationTime }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>申请人：</label>
              <span>{{ registrationInfo.applicant }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>联系电话：</label>
              <span>{{ registrationInfo.contactPhone }}</span>
            </div>
          </ElCol>
        </ElRow>
      </ElCard>

      <!-- 企业基本信息 -->
      <ElCard class="mb-20px">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:office-building" class="mr-5px" />
            企业基本信息
          </div>
        </template>
        <ElRow :gutter="20">
          <ElCol :span="12">
            <div class="info-item">
              <label>企业名称：</label>
              <span>{{ registrationInfo.enterpriseName }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>统一社会信用代码：</label>
              <span>{{ registrationInfo.creditCode }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>法定代表人：</label>
              <span>{{ registrationInfo.legalPerson }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>注册资本：</label>
              <span>{{ registrationInfo.registeredCapital }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>成立日期：</label>
              <span>{{ registrationInfo.establishmentDate }}</span>
            </div>
          </ElCol>
          <ElCol :span="12">
            <div class="info-item">
              <label>企业类型：</label>
              <span>{{ registrationInfo.enterpriseType }}</span>
            </div>
          </ElCol>
          <ElCol :span="24">
            <div class="info-item">
              <label>注册地址：</label>
              <span>{{ registrationInfo.registeredAddress }}</span>
            </div>
          </ElCol>
          <ElCol :span="24">
            <div class="info-item">
              <label>经营范围：</label>
              <span>{{ registrationInfo.businessScope }}</span>
            </div>
          </ElCol>
        </ElRow>
      </ElCard>

      <!-- 申请材料 -->
      <ElCard class="mb-20px">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:folder" class="mr-5px" />
            申请材料
          </div>
        </template>
        <div class="materials-list">
          <div
            v-for="material in registrationInfo.materials"
            :key="material.id"
            class="material-item"
          >
            <div class="material-info">
              <Icon icon="ep:document" class="mr-10px" />
              <span class="material-name">{{ material.name }}</span>
              <ElTag size="small" class="ml-10px">{{ material.type }}</ElTag>
            </div>
            <div class="material-actions">
              <ElButton type="primary" link @click="handlePreview(material)">
                <Icon icon="ep:view" class="mr-5px" />
                预览
              </ElButton>
              <ElButton type="success" link @click="handleDownload(material)">
                <Icon icon="ep:download" class="mr-5px" />
                下载
              </ElButton>
            </div>
          </div>
        </div>
      </ElCard>

      <!-- 审核记录 -->
      <ElCard class="mb-20px">
        <template #header>
          <div class="card-header">
            <Icon icon="ep:clock" class="mr-5px" />
            审核记录
          </div>
        </template>
        <ElTimeline>
          <ElTimelineItem
            v-for="record in auditRecords"
            :key="record.id"
            :timestamp="record.time"
            :type="getTimelineType(record.type)"
          >
            <div class="timeline-content">
              <div class="timeline-title">{{ record.title }}</div>
              <div class="timeline-desc">{{ record.description }}</div>
              <div class="timeline-operator">操作人：{{ record.operator }}</div>
            </div>
          </ElTimelineItem>
        </ElTimeline>
      </ElCard>

      <!-- 操作按钮 -->
      <div class="action-buttons" v-if="registrationInfo.auditStatus === '0'">
        <ElButton type="success" size="large" @click="handleApprove">
          <Icon icon="ep:check" class="mr-5px" />
          审核通过
        </ElButton>
        <ElButton type="danger" size="large" @click="handleReject">
          <Icon icon="ep:close" class="mr-5px" />
          审核拒绝
        </ElButton>
      </div>
    </ContentWrap>

    <!-- 审核对话框 -->
    <ElDialog
      v-model="auditDialogVisible"
      :title="auditType === 'approve' ? '审核通过' : '审核拒绝'"
      width="500px"
    >
      <ElForm :model="auditForm" label-width="80px">
        <ElFormItem label="审核意见" required>
          <ElInput
            v-model="auditForm.remark"
            type="textarea"
            :rows="4"
            :placeholder="auditType === 'approve' ? '请输入通过原因' : '请输入拒绝原因'"
          />
        </ElFormItem>
      </ElForm>
      <template #footer>
        <ElButton @click="auditDialogVisible = false">取消</ElButton>
        <ElButton
          type="primary"
          @click="confirmAudit"
          :loading="auditLoading"
        >
          确定
        </ElButton>
      </template>
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'

const router = useRouter()
const route = useRoute()

// 注册信息
const registrationInfo = ref({
  id: '',
  applicationNumber: '',
  applicationTime: '',
  applicant: '',
  contactPhone: '',
  enterpriseName: '',
  creditCode: '',
  legalPerson: '',
  registeredCapital: '',
  establishmentDate: '',
  enterpriseType: '',
  registeredAddress: '',
  businessScope: '',
  auditStatus: '0',
  materials: [] as any[]
})

// 审核记录
const auditRecords = ref([
  {
    id: '1',
    title: '提交申请',
    description: '企业提交碳账户注册申请',
    operator: '系统',
    time: '2024-07-25 10:30:00',
    type: 'primary'
  }
])

// 审核对话框
const auditDialogVisible = ref(false)
const auditType = ref<'approve' | 'reject'>('approve')
const auditLoading = ref(false)
const auditForm = reactive({
  remark: ''
})

// 状态相关方法
const getAuditStatusType = (status: string) => {
  const statusMap = {
    '0': 'warning',
    '1': 'success',
    '2': 'danger'
  }
  return statusMap[status] || 'info'
}

const getAuditStatusText = (status: string) => {
  const statusMap = {
    '0': '待审核',
    '1': '审核通过',
    '2': '审核拒绝'
  }
  return statusMap[status] || '未知'
}

const getTimelineType = (type: string) => {
  const typeMap = {
    'primary': 'primary',
    'success': 'success',
    'warning': 'warning',
    'danger': 'danger'
  }
  return typeMap[type] || 'primary'
}

// 获取注册详情
const getRegistrationDetail = async () => {
  const id = route.params.id as string
  // 模拟API调用
  setTimeout(() => {
    registrationInfo.value = {
      id: id,
      applicationNumber: 'REG2024072501',
      applicationTime: '2024-07-25 10:30:00',
      applicant: '赵六',
      contactPhone: '***********',
      enterpriseName: '新能源科技有限公司',
      creditCode: '91110000111111111A',
      legalPerson: '赵六',
      registeredCapital: '5000万元',
      establishmentDate: '2022-03-15',
      enterpriseType: '有限责任公司',
      registeredAddress: '北京市海淀区中关村科技园区创业大厦B座2001室',
      businessScope: '新能源技术开发、技术咨询、技术服务；太阳能设备销售；节能环保技术推广。',
      auditStatus: '0',
      materials: [
        {
          id: '1',
          name: '营业执照',
          type: 'PDF',
          url: '/files/business-license.pdf'
        },
        {
          id: '2',
          name: '法人身份证',
          type: 'PDF',
          url: '/files/legal-person-id.pdf'
        },
        {
          id: '3',
          name: '企业章程',
          type: 'PDF',
          url: '/files/company-charter.pdf'
        },
        {
          id: '4',
          name: '环保资质证书',
          type: 'PDF',
          url: '/files/environmental-certificate.pdf'
        }
      ]
    }
  }, 500)
}

// 事件处理方法
const handleBack = () => {
  router.back()
}

const handlePreview = (material: any) => {
  ElMessage.info(`预览文件：${material.name}`)
}

const handleDownload = (material: any) => {
  ElMessage.info(`下载文件：${material.name}`)
}

const handleApprove = () => {
  auditType.value = 'approve'
  auditForm.remark = ''
  auditDialogVisible.value = true
}

const handleReject = () => {
  auditType.value = 'reject'
  auditForm.remark = ''
  auditDialogVisible.value = true
}

const confirmAudit = async () => {
  if (!auditForm.remark.trim()) {
    ElMessage.warning('请输入审核意见')
    return
  }
  
  auditLoading.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新状态
    registrationInfo.value.auditStatus = auditType.value === 'approve' ? '1' : '2'
    
    // 添加审核记录
    auditRecords.value.push({
      id: Date.now().toString(),
      title: auditType.value === 'approve' ? '审核通过' : '审核拒绝',
      description: auditForm.remark,
      operator: '管理员',
      time: new Date().toLocaleString(),
      type: auditType.value === 'approve' ? 'success' : 'danger'
    })
    
    ElMessage.success(`审核${auditType.value === 'approve' ? '通过' : '拒绝'}成功`)
    auditDialogVisible.value = false
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    auditLoading.value = false
  }
}

onMounted(() => {
  getRegistrationDetail()
})
</script>

<style lang="less" scoped>
.registration-detail {
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    
    .header-title {
      display: flex;
      align-items: center;
      gap: 15px;
      
      h2 {
        margin: 0;
        color: #303133;
      }
    }
  }
  
  .card-header {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #303133;
  }
  
  .info-item {
    margin-bottom: 15px;
    
    label {
      font-weight: 600;
      color: #606266;
      margin-right: 10px;
    }
    
    span {
      color: #303133;
    }
  }
  
  .materials-list {
    .material-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 15px;
      border: 1px solid #ebeef5;
      border-radius: 8px;
      margin-bottom: 10px;
      
      &:hover {
        background-color: #f8f9fa;
      }
      
      .material-info {
        display: flex;
        align-items: center;
        
        .material-name {
          font-weight: 500;
          color: #303133;
        }
      }
      
      .material-actions {
        display: flex;
        gap: 10px;
      }
    }
  }
  
  .timeline-content {
    .timeline-title {
      font-weight: 600;
      color: #303133;
      margin-bottom: 5px;
    }
    
    .timeline-desc {
      color: #606266;
      margin-bottom: 5px;
    }
    
    .timeline-operator {
      font-size: 12px;
      color: #909399;
    }
  }
  
  .action-buttons {
    text-align: center;
    padding: 20px 0;
    
    .el-button {
      margin: 0 15px;
      min-width: 120px;
    }
  }
}
</style>
