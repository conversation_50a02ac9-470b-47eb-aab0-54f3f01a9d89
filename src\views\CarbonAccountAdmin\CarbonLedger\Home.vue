<template>
  <div class="carbon-ledger-home carbon-admin-layout">
    <ContentWrap>
      <!-- 统计概览 -->
      <div class="content-section">
        <ElRow :gutter="20">
          <ElCol :span="6">
            <ElCard class="stats-card total carbon-card">
              <div class="stats-content">
                <div class="stats-icon">
                  <Icon icon="ep:office-building" size="40" />
                </div>
                <div class="stats-info">
                  <div class="stats-number">{{ overview.totalEnterprises }}</div>
                  <div class="stats-label">注册企业总数</div>
                </div>
              </div>
            </ElCard>
          </ElCol>
          <ElCol :span="6">
            <ElCard class="stats-card active carbon-card">
              <div class="stats-content">
                <div class="stats-icon">
                  <Icon icon="ep:check" size="40" />
                </div>
                <div class="stats-info">
                  <div class="stats-number">{{ overview.activeEnterprises }}</div>
                  <div class="stats-label">活跃企业数</div>
                </div>
              </div>
            </ElCard>
          </ElCol>
          <ElCol :span="6">
            <ElCard class="stats-card carbon carbon-card">
              <div class="stats-content">
                <div class="stats-icon">
                  <Icon icon="ep:data-analysis" size="40" />
                </div>
                <div class="stats-info">
                  <div class="stats-number">{{ overview.totalCarbonData }}</div>
                  <div class="stats-label">碳数据记录</div>
                </div>
              </div>
            </ElCard>
          </ElCol>
          <ElCol :span="6">
            <ElCard class="stats-card emission carbon-card">
              <div class="stats-content">
                <div class="stats-icon">
                  <Icon icon="ep:warning" size="40" />
                </div>
                <div class="stats-info">
                  <div class="stats-number">{{ overview.totalEmissions }}</div>
                  <div class="stats-label">总碳排放量(吨)</div>
                </div>
              </div>
            </ElCard>
          </ElCol>
        </ElRow>
      </div>

      <!-- 快速操作 -->
      <div class="content-section">
        <ElCard class="carbon-card">
          <template #header>
            <div class="card-header">
              <Icon icon="ep:lightning" class="header-icon" />
              快速操作
            </div>
          </template>
          <ElRow :gutter="20">
            <ElCol :span="6">
              <div class="quick-action-item" @click="handleQuickSearch">
                <Icon icon="ep:search" size="30" />
                <div class="action-label">企业搜索</div>
              </div>
            </ElCol>
            <ElCol :span="6">
              <div class="action-item" @click="handleDataExport">
                <Icon icon="ep:download" size="30" />
                <div class="action-label">数据导出</div>
              </div>
            </ElCol>
            <ElCol :span="6">
              <div class="action-item" @click="handleReportGenerate">
                <Icon icon="ep:document" size="30" />
                <div class="action-label">生成报告</div>
              </div>
            </ElCol>
            <ElCol :span="6">
              <div class="action-item" @click="handleSystemSettings">
                <Icon icon="ep:setting" size="30" />
                <div class="action-label">系统设置</div>
              </div>
            </ElCol>
          </ElRow>
        </ElCard>
      </div>

      <!-- 最新企业 -->
      <div class="recent-enterprises mb-20px">
        <ElCard>
          <template #header>
            <div class="card-header">
              <Icon icon="ep:timer" class="mr-5px" />
              最新注册企业
              <ElButton type="primary" link class="ml-auto" @click="handleViewAllEnterprises">
                查看全部
              </ElButton>
            </div>
          </template>
          <ElTable :data="recentEnterprises" style="width: 100%">
            <ElTableColumn prop="enterpriseName" label="企业名称" min-width="200" />
            <ElTableColumn prop="industry" label="所属行业" width="120" />
            <ElTableColumn prop="registrationTime" label="注册时间" width="150" />
            <ElTableColumn prop="status" label="状态" width="100">
              <template #default="{ row }">
                <ElTag :type="getStatusType(row.status)">
                  {{ getStatusText(row.status) }}
                </ElTag>
              </template>
            </ElTableColumn>
            <ElTableColumn label="操作" width="120">
              <template #default="{ row }">
                <ElButton type="primary" link @click="handleViewEnterprise(row)">
                  查看详情
                </ElButton>
              </template>
            </ElTableColumn>
          </ElTable>
        </ElCard>
      </div>

      <!-- 数据趋势图表 -->
      <ElRow :gutter="20">
        <ElCol :span="12">
          <ElCard>
            <template #header>
              <div class="card-header">
                <Icon icon="ep:trend-charts" class="mr-5px" />
                企业注册趋势
              </div>
            </template>
            <div class="chart-container">
              <div ref="registrationChartRef" style="width: 100%; height: 300px;"></div>
            </div>
          </ElCard>
        </ElCol>
        <ElCol :span="12">
          <ElCard>
            <template #header>
              <div class="card-header">
                <Icon icon="ep:pie-chart" class="mr-5px" />
                行业分布
              </div>
            </template>
            <div class="chart-container">
              <div ref="industryChartRef" style="width: 100%; height: 300px;"></div>
            </div>
          </ElCard>
        </ElCol>
      </ElRow>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'

const router = useRouter()

// 图表引用
const registrationChartRef = ref()
const industryChartRef = ref()

// 统计概览数据
const overview = ref({
  totalEnterprises: 1256,
  activeEnterprises: 1089,
  totalCarbonData: 45678,
  totalEmissions: 123456.78
})

// 最新企业数据
const recentEnterprises = ref([
  {
    id: '1',
    enterpriseName: '新能源科技有限公司',
    industry: '新能源',
    registrationTime: '2024-07-28',
    status: '1'
  },
  {
    id: '2',
    enterpriseName: '智能制造股份有限公司',
    industry: '制造业',
    registrationTime: '2024-07-27',
    status: '1'
  },
  {
    id: '3',
    enterpriseName: '绿色建筑工程有限公司',
    industry: '建筑业',
    registrationTime: '2024-07-26',
    status: '0'
  },
  {
    id: '4',
    enterpriseName: '环保科技发展有限公司',
    industry: '环保',
    registrationTime: '2024-07-25',
    status: '1'
  },
  {
    id: '5',
    enterpriseName: '清洁能源投资有限公司',
    industry: '投资',
    registrationTime: '2024-07-24',
    status: '1'
  }
])

// 状态相关方法
const getStatusType = (status: string) => {
  const statusMap = {
    '0': 'warning',
    '1': 'success',
    '2': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    '0': '待审核',
    '1': '正常',
    '2': '冻结'
  }
  return statusMap[status] || '未知'
}

// 初始化图表
const initCharts = async () => {
  await nextTick()
  
  // 这里应该使用真实的图表库，如 ECharts
  // 由于没有引入图表库，这里只是模拟
  if (registrationChartRef.value) {
    registrationChartRef.value.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #909399;">企业注册趋势图表</div>'
  }
  
  if (industryChartRef.value) {
    industryChartRef.value.innerHTML = '<div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #909399;">行业分布图表</div>'
  }
}

// 事件处理方法
const handleQuickSearch = () => {
  router.push('/carbon-account-admin/carbon-ledger/search')
}

const handleDataExport = () => {
  ElMessage.info('数据导出功能待实现')
}

const handleReportGenerate = () => {
  ElMessage.info('报告生成功能待实现')
}

const handleSystemSettings = () => {
  ElMessage.info('系统设置功能待实现')
}

const handleViewAllEnterprises = () => {
  router.push('/carbon-account-admin/enterprise-account/list')
}

const handleViewEnterprise = (row: any) => {
  router.push(`/carbon-account-admin/carbon-ledger/enterprise-info/${row.id}`)
}

onMounted(() => {
  initCharts()
})
</script>

<style lang="less" scoped>
.carbon-ledger-home {
  .stats-overview {
    .stats-card {
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
      
      &.total {
        border-left: 4px solid #409eff;
      }
      
      &.active {
        border-left: 4px solid #67c23a;
      }
      
      &.carbon {
        border-left: 4px solid #e6a23c;
      }
      
      &.emission {
        border-left: 4px solid #f56c6c;
      }
      
      .stats-content {
        display: flex;
        align-items: center;
        gap: 15px;
        
        .stats-icon {
          color: #409eff;
        }
        
        .stats-info {
          .stats-number {
            font-size: 24px;
            font-weight: bold;
            color: #303133;
            margin-bottom: 5px;
          }
          
          .stats-label {
            font-size: 14px;
            color: #909399;
          }
        }
      }
    }
  }
  
  .quick-actions {
    .action-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      padding: 20px;
      border: 1px solid #ebeef5;
      border-radius: 8px;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        border-color: #409eff;
        background-color: #f0f9ff;
        transform: translateY(-2px);
      }
      
      .action-label {
        margin-top: 10px;
        font-size: 14px;
        color: #606266;
      }
    }
  }
  
  .card-header {
    display: flex;
    align-items: center;
    font-weight: 600;
    color: #303133;
    
    .ml-auto {
      margin-left: auto;
    }
  }
  
  .chart-container {
    padding: 10px 0;
  }
}
</style>
