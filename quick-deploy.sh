#!/bin/bash

# 碳账户管理系统快速部署脚本 - 仅前端
# 使用方法: ./quick-deploy.sh

set -e

# 颜色定义
GREEN='\033[0;32m'
BLUE='\033[0;34m'
RED='\033[0;31m'
NC='\033[0m'

echo -e "${BLUE}🚀 碳账户管理系统 - 快速部署${NC}"
echo "=================================="

# 检查 Docker
if ! command -v docker &> /dev/null; then
    echo -e "${RED}❌ Docker 未安装，请先安装 Docker${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Docker 检查通过${NC}"

# 构建镜像
echo -e "${BLUE}📦 构建前端镜像...${NC}"
docker-compose -f docker-compose.simple.yml build

# 启动服务
echo -e "${BLUE}🔄 启动前端服务...${NC}"
docker-compose -f docker-compose.simple.yml up -d

# 等待服务启动
echo -e "${BLUE}⏳ 等待服务启动...${NC}"
sleep 10

# 检查服务状态
if docker-compose -f docker-compose.simple.yml ps | grep -q "Up"; then
    echo -e "${GREEN}✅ 服务启动成功！${NC}"
    echo ""
    echo "🌐 访问地址: http://localhost"
    echo "📊 服务状态: docker-compose -f docker-compose.simple.yml ps"
    echo "📋 查看日志: docker-compose -f docker-compose.simple.yml logs -f"
    echo "🛑 停止服务: docker-compose -f docker-compose.simple.yml down"
else
    echo -e "${RED}❌ 服务启动失败，请检查日志${NC}"
    docker-compose -f docker-compose.simple.yml logs
    exit 1
fi
