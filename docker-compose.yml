version: '3.8'

services:
  # 前端应用
  carbon-account-frontend:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: carbon-account-frontend
    restart: unless-stopped
    ports:
      - "80:80"
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - ./docker/nginx.conf:/etc/nginx/nginx.conf:ro
      - nginx_logs:/var/log/nginx
    networks:
      - carbon-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.carbon-frontend.rule=Host(`localhost`)"
      - "traefik.http.services.carbon-frontend.loadbalancer.server.port=80"

  # 后端 API 服务（示例配置，根据实际后端调整）
  carbon-account-backend:
    image: node:18-alpine
    container_name: carbon-account-backend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - TZ=Asia/Shanghai
      - DATABASE_URL=******************************************************/carbon_account
      - REDIS_URL=redis://redis:6379
    volumes:
      - ./backend:/app
      - backend_node_modules:/app/node_modules
    working_dir: /app
    command: sh -c "npm install && npm run start"
    networks:
      - carbon-network
    depends_on:
      - postgres
      - redis
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s

  # PostgreSQL 数据库
  postgres:
    image: postgres:15-alpine
    container_name: carbon-postgres
    restart: unless-stopped
    environment:
      - POSTGRES_DB=carbon_account
      - POSTGRES_USER=carbon_user
      - POSTGRES_PASSWORD=carbon_password
      - TZ=Asia/Shanghai
      - PGTZ=Asia/Shanghai
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./docker/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - carbon-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U carbon_user -d carbon_account"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis 缓存
  redis:
    image: redis:7-alpine
    container_name: carbon-redis
    restart: unless-stopped
    environment:
      - TZ=Asia/Shanghai
    volumes:
      - redis_data:/data
      - ./docker/redis.conf:/usr/local/etc/redis/redis.conf:ro
    ports:
      - "6379:6379"
    networks:
      - carbon-network
    command: redis-server /usr/local/etc/redis/redis.conf
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx 负载均衡器（可选）
  nginx-lb:
    image: nginx:alpine
    container_name: carbon-nginx-lb
    restart: unless-stopped
    ports:
      - "8080:80"
    volumes:
      - ./docker/nginx-lb.conf:/etc/nginx/nginx.conf:ro
    networks:
      - carbon-network
    depends_on:
      - carbon-account-frontend
    profiles:
      - loadbalancer

  # 监控服务 - Prometheus（可选）
  prometheus:
    image: prom/prometheus:latest
    container_name: carbon-prometheus
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./docker/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_data:/prometheus
    networks:
      - carbon-network
    profiles:
      - monitoring

  # 监控服务 - Grafana（可选）
  grafana:
    image: grafana/grafana:latest
    container_name: carbon-grafana
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=admin123
    volumes:
      - grafana_data:/var/lib/grafana
    networks:
      - carbon-network
    profiles:
      - monitoring

networks:
  carbon-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  nginx_logs:
    driver: local
  backend_node_modules:
    driver: local
  prometheus_data:
    driver: local
  grafana_data:
    driver: local
