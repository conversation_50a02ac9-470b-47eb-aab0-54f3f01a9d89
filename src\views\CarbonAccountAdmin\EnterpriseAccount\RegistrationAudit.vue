<template>
  <div class="registration-audit">
    <ContentWrap>
      <!-- 搜索区域 -->
      <div class="search-area mb-20px">
        <ElForm :model="searchForm" inline>
          <ElFormItem label="企业名称">
            <ElInput
              v-model="searchForm.enterpriseName"
              placeholder="请输入企业名称"
              clearable
              style="width: 200px"
            />
          </ElFormItem>
          <ElFormItem label="审核状态">
            <ElSelect
              v-model="searchForm.auditStatus"
              placeholder="请选择状态"
              clearable
              style="width: 150px"
            >
              <ElOption label="全部" value="" />
              <ElOption label="待审核" value="0" />
              <ElOption label="审核通过" value="1" />
              <ElOption label="审核拒绝" value="2" />
            </ElSelect>
          </ElFormItem>
          <ElFormItem label="申请时间">
            <ElDatePicker
              v-model="searchForm.dateRange"
              type="daterange"
              range-separator="至"
              start-placeholder="开始日期"
              end-placeholder="结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
            />
          </ElFormItem>
          <ElFormItem>
            <ElButton type="primary" @click="handleSearch">
              <Icon icon="ep:search" class="mr-5px" />
              搜索
            </ElButton>
            <ElButton @click="handleReset">
              <Icon icon="ep:refresh" class="mr-5px" />
              重置
            </ElButton>
          </ElFormItem>
        </ElForm>
      </div>

      <!-- 统计信息 -->
      <div class="stats-area mb-20px">
        <ElRow :gutter="20">
          <ElCol :span="6">
            <ElCard class="stats-card">
              <div class="stats-item">
                <div class="stats-number">{{ stats.total }}</div>
                <div class="stats-label">总申请数</div>
              </div>
            </ElCard>
          </ElCol>
          <ElCol :span="6">
            <ElCard class="stats-card pending">
              <div class="stats-item">
                <div class="stats-number">{{ stats.pending }}</div>
                <div class="stats-label">待审核</div>
              </div>
            </ElCard>
          </ElCol>
          <ElCol :span="6">
            <ElCard class="stats-card approved">
              <div class="stats-item">
                <div class="stats-number">{{ stats.approved }}</div>
                <div class="stats-label">已通过</div>
              </div>
            </ElCard>
          </ElCol>
          <ElCol :span="6">
            <ElCard class="stats-card rejected">
              <div class="stats-item">
                <div class="stats-number">{{ stats.rejected }}</div>
                <div class="stats-label">已拒绝</div>
              </div>
            </ElCard>
          </ElCol>
        </ElRow>
      </div>

      <!-- 表格区域 -->
      <Table
        v-model:pageSize="tableObject.pageSize"
        v-model:currentPage="tableObject.currentPage"
        :columns="columns"
        :data="tableObject.tableList"
        :loading="tableObject.loading"
        :pagination="{
          total: tableObject.total
        }"
        @register="register"
      >
        <template #auditStatus="{ row }">
          <ElTag :type="getAuditStatusType(row.auditStatus)">
            {{ getAuditStatusText(row.auditStatus) }}
          </ElTag>
        </template>
        <template #action="{ row }">
          <ElButton type="primary" link @click="handleDetail(row)">
            <Icon icon="ep:view" class="mr-5px" />
            查看详情
          </ElButton>
          <ElButton
            type="success"
            link
            @click="handleApprove(row)"
            v-if="row.auditStatus === '0'"
          >
            <Icon icon="ep:check" class="mr-5px" />
            通过
          </ElButton>
          <ElButton
            type="danger"
            link
            @click="handleReject(row)"
            v-if="row.auditStatus === '0'"
          >
            <Icon icon="ep:close" class="mr-5px" />
            拒绝
          </ElButton>
        </template>
      </Table>
    </ContentWrap>

    <!-- 审核对话框 -->
    <ElDialog
      v-model="auditDialogVisible"
      :title="auditType === 'approve' ? '审核通过' : '审核拒绝'"
      width="500px"
    >
      <ElForm :model="auditForm" label-width="80px">
        <ElFormItem label="审核意见" required>
          <ElInput
            v-model="auditForm.remark"
            type="textarea"
            :rows="4"
            :placeholder="auditType === 'approve' ? '请输入通过原因' : '请输入拒绝原因'"
          />
        </ElFormItem>
      </ElForm>
      <template #footer>
        <ElButton @click="auditDialogVisible = false">取消</ElButton>
        <ElButton
          type="primary"
          @click="confirmAudit"
          :loading="auditLoading"
        >
          确定
        </ElButton>
      </template>
    </ElDialog>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Table } from '@/components/Table'
import { useTable } from '@/hooks/web/useTable'
import { Icon } from '@/components/Icon'

const router = useRouter()

// 搜索表单
const searchForm = reactive({
  enterpriseName: '',
  auditStatus: '',
  dateRange: []
})

// 统计数据
const stats = ref({
  total: 0,
  pending: 0,
  approved: 0,
  rejected: 0
})

// 审核对话框
const auditDialogVisible = ref(false)
const auditType = ref<'approve' | 'reject'>('approve')
const auditLoading = ref(false)
const currentRow = ref<any>(null)
const auditForm = reactive({
  remark: ''
})

// 表格配置
const columns = [
  {
    field: 'index',
    label: '序号',
    type: 'index',
    width: 80
  },
  {
    field: 'enterpriseName',
    label: '企业名称',
    minWidth: 200
  },
  {
    field: 'creditCode',
    label: '统一社会信用代码',
    minWidth: 180
  },
  {
    field: 'legalPerson',
    label: '法定代表人',
    width: 120
  },
  {
    field: 'contactPhone',
    label: '联系电话',
    width: 130
  },
  {
    field: 'applicationTime',
    label: '申请时间',
    width: 120
  },
  {
    field: 'auditStatus',
    label: '审核状态',
    width: 100,
    slots: {
      default: 'auditStatus'
    }
  },
  {
    field: 'action',
    label: '操作',
    width: 200,
    slots: {
      default: 'action'
    }
  }
]

// 表格钩子
const { register, tableObject, methods } = useTable({
  getListApi: getRegistrationList
})

const { getList } = methods

// 模拟API
async function getRegistrationList(params: any) {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = {
        list: [
          {
            id: '1',
            enterpriseName: '新能源科技有限公司',
            creditCode: '91110000111111111A',
            legalPerson: '赵六',
            contactPhone: '13800138004',
            applicationTime: '2024-07-25',
            auditStatus: '0'
          },
          {
            id: '2',
            enterpriseName: '智能制造股份有限公司',
            creditCode: '91110000222222222B',
            legalPerson: '孙七',
            contactPhone: '13800138005',
            applicationTime: '2024-07-26',
            auditStatus: '1'
          },
          {
            id: '3',
            enterpriseName: '绿色建筑工程有限公司',
            creditCode: '91110000333333333C',
            legalPerson: '周八',
            contactPhone: '13800138006',
            applicationTime: '2024-07-27',
            auditStatus: '2'
          }
        ],
        total: 3
      }
      
      // 更新统计数据
      stats.value = {
        total: mockData.list.length,
        pending: mockData.list.filter(item => item.auditStatus === '0').length,
        approved: mockData.list.filter(item => item.auditStatus === '1').length,
        rejected: mockData.list.filter(item => item.auditStatus === '2').length
      }
      
      resolve(mockData)
    }, 500)
  })
}

// 状态相关方法
const getAuditStatusType = (status: string) => {
  const statusMap = {
    '0': 'warning',
    '1': 'success',
    '2': 'danger'
  }
  return statusMap[status] || 'info'
}

const getAuditStatusText = (status: string) => {
  const statusMap = {
    '0': '待审核',
    '1': '审核通过',
    '2': '审核拒绝'
  }
  return statusMap[status] || '未知'
}

// 事件处理方法
const handleSearch = () => {
  getList()
}

const handleReset = () => {
  Object.assign(searchForm, {
    enterpriseName: '',
    auditStatus: '',
    dateRange: []
  })
  getList()
}

const handleDetail = (row: any) => {
  router.push(`/carbon-account-admin/enterprise-account/registration-detail/${row.id}`)
}

const handleApprove = (row: any) => {
  currentRow.value = row
  auditType.value = 'approve'
  auditForm.remark = ''
  auditDialogVisible.value = true
}

const handleReject = (row: any) => {
  currentRow.value = row
  auditType.value = 'reject'
  auditForm.remark = ''
  auditDialogVisible.value = true
}

const confirmAudit = async () => {
  if (!auditForm.remark.trim()) {
    ElMessage.warning('请输入审核意见')
    return
  }
  
  auditLoading.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 更新状态
    if (currentRow.value) {
      currentRow.value.auditStatus = auditType.value === 'approve' ? '1' : '2'
    }
    
    ElMessage.success(`审核${auditType.value === 'approve' ? '通过' : '拒绝'}成功`)
    auditDialogVisible.value = false
    getList()
  } catch (error) {
    ElMessage.error('操作失败')
  } finally {
    auditLoading.value = false
  }
}

onMounted(() => {
  getList()
})
</script>

<style lang="less" scoped>
.registration-audit {
  .search-area {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
  }
  
  .stats-area {
    .stats-card {
      text-align: center;
      cursor: pointer;
      transition: all 0.3s;
      
      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      }
      
      &.pending {
        border-left: 4px solid #e6a23c;
      }
      
      &.approved {
        border-left: 4px solid #67c23a;
      }
      
      &.rejected {
        border-left: 4px solid #f56c6c;
      }
      
      .stats-item {
        .stats-number {
          font-size: 28px;
          font-weight: bold;
          color: #303133;
          margin-bottom: 8px;
        }
        
        .stats-label {
          font-size: 14px;
          color: #909399;
        }
      }
    }
  }
}
</style>
