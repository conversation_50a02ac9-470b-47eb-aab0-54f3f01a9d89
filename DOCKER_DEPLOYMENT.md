# 碳账户管理系统 Docker 部署指南

## 概述

本文档提供了碳账户管理系统的完整 Docker 部署方案，支持一键部署、监控、负载均衡等功能。

## 系统架构

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Nginx LB      │    │   Frontend      │    │   Backend       │
│   (可选)        │────│   (Vue3 + TS)   │────│   (Node.js)     │
│   Port: 8080    │    │   Port: 80      │    │   Port: 3000    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │                       │
                                └───────────┬───────────┘
                                            │
                       ┌─────────────────┐  │  ┌─────────────────┐
                       │   PostgreSQL    │  │  │     Redis       │
                       │   Port: 5432    │  │  │   Port: 6379    │
                       └─────────────────┘  │  └─────────────────┘
                                            │
                       ┌─────────────────┐  │  ┌─────────────────┐
                       │   Prometheus    │  │  │    Grafana      │
                       │   Port: 9090    │  │  │   Port: 3001    │
                       │   (监控可选)     │  │  │   (监控可选)     │
                       └─────────────────┘  │  └─────────────────┘
```

## 快速开始

### 1. 系统要求

- Docker 20.10+
- Docker Compose 2.0+
- 至少 4GB 可用内存
- 至少 10GB 可用磁盘空间

### 2. 一键部署

```bash
# 1. 克隆项目（如果还没有）
git clone <repository-url>
cd carbon-account

# 2. 给部署脚本执行权限
chmod +x deploy.sh

# 3. 初始化项目
./deploy.sh init

# 4. 构建并启动服务
./deploy.sh build
./deploy.sh start

# 5. 查看服务状态
./deploy.sh status
```

### 3. 访问应用

- **前端应用**: http://localhost
- **后端 API**: http://localhost:3000
- **数据库**: localhost:5432
- **Redis**: localhost:6379

## 部署配置

### 环境变量配置

部署脚本会自动创建 `.env.docker` 文件，包含以下配置：

```bash
# 数据库配置
POSTGRES_DB=carbon_account
POSTGRES_USER=carbon_user
POSTGRES_PASSWORD=carbon_password_<timestamp>

# Redis 配置
REDIS_PASSWORD=redis_password_<timestamp>

# 应用配置
NODE_ENV=production
TZ=Asia/Shanghai

# 监控配置
GRAFANA_ADMIN_PASSWORD=admin123
```

### 服务配置

#### 基础服务
- `carbon-account-frontend`: Vue3 前端应用
- `carbon-account-backend`: Node.js 后端 API（示例配置）
- `postgres`: PostgreSQL 数据库
- `redis`: Redis 缓存

#### 可选服务（Profile）

**监控服务** (`monitoring` profile):
```bash
./deploy.sh start monitoring
```
- `prometheus`: 指标收集 (http://localhost:9090)
- `grafana`: 监控面板 (http://localhost:3001, admin/admin123)

**负载均衡** (`loadbalancer` profile):
```bash
./deploy.sh start loadbalancer
```
- `nginx-lb`: Nginx 负载均衡器 (http://localhost:8080)

## 部署脚本使用

### 基本命令

```bash
# 初始化项目
./deploy.sh init

# 构建镜像
./deploy.sh build

# 启动服务
./deploy.sh start                    # 基础服务
./deploy.sh start monitoring         # 包含监控
./deploy.sh start loadbalancer       # 包含负载均衡

# 停止服务
./deploy.sh stop

# 重启服务
./deploy.sh restart

# 查看状态
./deploy.sh status

# 查看日志
./deploy.sh logs                     # 所有服务
./deploy.sh logs carbon-account-frontend  # 指定服务
```

### 数据管理

```bash
# 备份数据
./deploy.sh backup

# 恢复数据
./deploy.sh restore backups/20241129_143000

# 清理资源
./deploy.sh cleanup
```

## 手动部署

如果不使用部署脚本，可以手动执行以下命令：

```bash
# 1. 构建镜像
docker-compose build

# 2. 启动基础服务
docker-compose up -d carbon-account-frontend postgres redis

# 3. 启动监控服务（可选）
docker-compose --profile monitoring up -d

# 4. 启动负载均衡（可选）
docker-compose --profile loadbalancer up -d

# 5. 查看状态
docker-compose ps
```

## 数据持久化

系统使用 Docker volumes 进行数据持久化：

- `postgres_data`: PostgreSQL 数据
- `redis_data`: Redis 数据
- `nginx_logs`: Nginx 日志
- `prometheus_data`: Prometheus 数据
- `grafana_data`: Grafana 配置和面板

## 网络配置

系统使用自定义网络 `carbon-network` (**********/16)，所有服务都在此网络中通信。

## 安全配置

### 数据库安全
- 使用强密码（自动生成）
- 限制网络访问
- 定期备份

### 应用安全
- Nginx 安全头配置
- API 限流保护
- HTTPS 支持（生产环境建议）

### 监控安全
- Grafana 管理员密码保护
- Prometheus 访问控制

## 性能优化

### 前端优化
- Nginx Gzip 压缩
- 静态资源缓存
- CDN 支持（可配置）

### 后端优化
- Redis 缓存
- 数据库连接池
- API 限流

### 数据库优化
- PostgreSQL 索引优化
- 连接池配置
- 定期维护

## 监控和日志

### 应用监控
- Prometheus 指标收集
- Grafana 可视化面板
- 健康检查

### 日志管理
```bash
# 查看实时日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f carbon-account-frontend

# 日志文件位置
docker volume inspect carbon-account_nginx_logs
```

### 健康检查
所有服务都配置了健康检查：
```bash
# 检查服务健康状态
docker-compose ps
```

## 故障排除

### 常见问题

1. **端口冲突**
   ```bash
   # 修改 docker-compose.yml 中的端口映射
   ports:
     - "8080:80"  # 改为其他端口
   ```

2. **内存不足**
   ```bash
   # 检查系统资源
   docker system df
   docker stats
   ```

3. **数据库连接失败**
   ```bash
   # 检查数据库状态
   docker-compose logs postgres
   
   # 重启数据库
   docker-compose restart postgres
   ```

4. **前端无法访问**
   ```bash
   # 检查 Nginx 配置
   docker-compose logs carbon-account-frontend
   
   # 重新构建前端
   docker-compose build carbon-account-frontend
   ```

### 调试模式

```bash
# 以调试模式启动
docker-compose up --no-deps carbon-account-frontend

# 进入容器调试
docker-compose exec carbon-account-frontend sh
```

## 生产环境部署

### 环境配置
1. 修改 `.env.docker` 中的密码
2. 配置 HTTPS 证书
3. 设置域名和 DNS
4. 配置防火墙规则

### 安全加固
1. 使用非 root 用户运行
2. 限制容器权限
3. 定期更新镜像
4. 配置日志轮转

### 备份策略
1. 定期数据库备份
2. 配置文件备份
3. 镜像版本管理
4. 灾难恢复计划

## 更新和维护

### 应用更新
```bash
# 1. 拉取最新代码
git pull

# 2. 重新构建镜像
./deploy.sh build

# 3. 重启服务
./deploy.sh restart
```

### 数据库维护
```bash
# 数据库备份
./deploy.sh backup

# 数据库优化
docker-compose exec postgres psql -U carbon_user -d carbon_account -c "VACUUM ANALYZE;"
```

### 系统清理
```bash
# 清理未使用的镜像和容器
docker system prune -f

# 清理未使用的卷
docker volume prune -f
```

## 支持和帮助

如果遇到问题，请：

1. 查看日志文件
2. 检查系统资源
3. 参考故障排除部分
4. 联系技术支持

---

**注意**: 本部署方案适用于开发和测试环境。生产环境部署请根据实际需求进行安全加固和性能优化。
