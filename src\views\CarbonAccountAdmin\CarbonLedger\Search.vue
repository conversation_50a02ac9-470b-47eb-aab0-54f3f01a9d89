<template>
  <div class="carbon-ledger-search">
    <ContentWrap>
      <!-- 搜索区域 -->
      <div class="search-section mb-20px">
        <ElCard>
          <template #header>
            <div class="card-header">
              <Icon icon="ep:search" class="mr-5px" />
              企业碳账本搜索
            </div>
          </template>
          <ElForm :model="searchForm" label-width="100px">
            <ElRow :gutter="20">
              <ElCol :span="8">
                <ElFormItem label="企业名称">
                  <ElInput
                    v-model="searchForm.enterpriseName"
                    placeholder="请输入企业名称"
                    clearable
                  />
                </ElFormItem>
              </ElCol>
              <ElCol :span="8">
                <ElFormItem label="信用代码">
                  <ElInput
                    v-model="searchForm.creditCode"
                    placeholder="请输入统一社会信用代码"
                    clearable
                  />
                </ElFormItem>
              </ElCol>
              <ElCol :span="8">
                <ElFormItem label="所属行业">
                  <ElSelect
                    v-model="searchForm.industry"
                    placeholder="请选择行业"
                    clearable
                    style="width: 100%"
                  >
                    <ElOption label="全部" value="" />
                    <ElOption label="制造业" value="manufacturing" />
                    <ElOption label="能源" value="energy" />
                    <ElOption label="建筑业" value="construction" />
                    <ElOption label="交通运输" value="transportation" />
                    <ElOption label="环保" value="environmental" />
                    <ElOption label="新能源" value="renewable" />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
              <ElCol :span="8">
                <ElFormItem label="企业规模">
                  <ElSelect
                    v-model="searchForm.scale"
                    placeholder="请选择企业规模"
                    clearable
                    style="width: 100%"
                  >
                    <ElOption label="全部" value="" />
                    <ElOption label="大型企业" value="large" />
                    <ElOption label="中型企业" value="medium" />
                    <ElOption label="小型企业" value="small" />
                    <ElOption label="微型企业" value="micro" />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
              <ElCol :span="8">
                <ElFormItem label="注册地区">
                  <ElCascader
                    v-model="searchForm.region"
                    :options="regionOptions"
                    placeholder="请选择注册地区"
                    clearable
                    style="width: 100%"
                  />
                </ElFormItem>
              </ElCol>
              <ElCol :span="8">
                <ElFormItem label="碳排放量">
                  <ElSelect
                    v-model="searchForm.emissionRange"
                    placeholder="请选择排放量范围"
                    clearable
                    style="width: 100%"
                  >
                    <ElOption label="全部" value="" />
                    <ElOption label="0-1000吨" value="0-1000" />
                    <ElOption label="1000-5000吨" value="1000-5000" />
                    <ElOption label="5000-10000吨" value="5000-10000" />
                    <ElOption label="10000吨以上" value="10000+" />
                  </ElSelect>
                </ElFormItem>
              </ElCol>
            </ElRow>
            <ElRow>
              <ElCol :span="24" class="text-center">
                <ElButton type="primary" @click="handleSearch" :loading="searchLoading">
                  <Icon icon="ep:search" class="mr-5px" />
                  搜索
                </ElButton>
                <ElButton @click="handleReset">
                  <Icon icon="ep:refresh" class="mr-5px" />
                  重置
                </ElButton>
                <ElButton type="success" @click="handleAdvancedSearch">
                  <Icon icon="ep:setting" class="mr-5px" />
                  高级搜索
                </ElButton>
              </ElCol>
            </ElRow>
          </ElForm>
        </ElCard>
      </div>

      <!-- 搜索结果 -->
      <div class="search-results" v-if="searchResults.length > 0">
        <ElCard>
          <template #header>
            <div class="card-header">
              <Icon icon="ep:document" class="mr-5px" />
              搜索结果 (共{{ searchResults.length }}条)
              <div class="header-actions">
                <ElButton type="success" @click="handleExportResults">
                  <Icon icon="ep:download" class="mr-5px" />
                  导出结果
                </ElButton>
                <ElButton type="primary" @click="handleBatchCompare">
                  <Icon icon="ep:data-analysis" class="mr-5px" />
                  批量对比
                </ElButton>
              </div>
            </div>
          </template>
          
          <!-- 结果列表 -->
          <div class="results-list">
            <div
              v-for="enterprise in searchResults"
              :key="enterprise.id"
              class="result-item"
              @click="handleViewDetail(enterprise)"
            >
              <div class="enterprise-info">
                <div class="enterprise-header">
                  <h3 class="enterprise-name">{{ enterprise.enterpriseName }}</h3>
                  <ElTag :type="getStatusType(enterprise.status)" size="small">
                    {{ getStatusText(enterprise.status) }}
                  </ElTag>
                </div>
                <div class="enterprise-details">
                  <ElRow :gutter="20">
                    <ElCol :span="6">
                      <div class="detail-item">
                        <label>信用代码：</label>
                        <span>{{ enterprise.creditCode }}</span>
                      </div>
                    </ElCol>
                    <ElCol :span="6">
                      <div class="detail-item">
                        <label>所属行业：</label>
                        <span>{{ enterprise.industry }}</span>
                      </div>
                    </ElCol>
                    <ElCol :span="6">
                      <div class="detail-item">
                        <label>企业规模：</label>
                        <span>{{ enterprise.scale }}</span>
                      </div>
                    </ElCol>
                    <ElCol :span="6">
                      <div class="detail-item">
                        <label>注册时间：</label>
                        <span>{{ enterprise.registrationTime }}</span>
                      </div>
                    </ElCol>
                  </ElRow>
                </div>
                <div class="carbon-data">
                  <ElRow :gutter="20">
                    <ElCol :span="6">
                      <div class="carbon-item">
                        <div class="carbon-value">{{ enterprise.totalEmissions }}</div>
                        <div class="carbon-label">总排放量(吨)</div>
                      </div>
                    </ElCol>
                    <ElCol :span="6">
                      <div class="carbon-item">
                        <div class="carbon-value">{{ enterprise.yearEmissions }}</div>
                        <div class="carbon-label">年度排放量(吨)</div>
                      </div>
                    </ElCol>
                    <ElCol :span="6">
                      <div class="carbon-item">
                        <div class="carbon-value">{{ enterprise.reductionRate }}%</div>
                        <div class="carbon-label">减排率</div>
                      </div>
                    </ElCol>
                    <ElCol :span="6">
                      <div class="carbon-item">
                        <div class="carbon-value">{{ enterprise.lastUpdateTime }}</div>
                        <div class="carbon-label">最后更新</div>
                      </div>
                    </ElCol>
                  </ElRow>
                </div>
              </div>
              <div class="enterprise-actions">
                <ElButton type="primary" @click.stop="handleViewDetail(enterprise)">
                  查看详情
                </ElButton>
                <ElButton type="success" @click.stop="handleViewReport(enterprise)">
                  查看报告
                </ElButton>
                <ElButton type="warning" @click.stop="handleCompare(enterprise)">
                  加入对比
                </ElButton>
              </div>
            </div>
          </div>

          <!-- 分页 -->
          <div class="pagination-wrapper">
            <ElPagination
              v-model:current-page="pagination.currentPage"
              v-model:page-size="pagination.pageSize"
              :page-sizes="[10, 20, 50, 100]"
              :total="pagination.total"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </ElCard>
      </div>

      <!-- 空状态 -->
      <div class="empty-state" v-else-if="hasSearched">
        <ElEmpty description="未找到符合条件的企业" />
      </div>
    </ContentWrap>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { ContentWrap } from '@/components/ContentWrap'
import { Icon } from '@/components/Icon'

const router = useRouter()

// 搜索表单
const searchForm = reactive({
  enterpriseName: '',
  creditCode: '',
  industry: '',
  scale: '',
  region: [],
  emissionRange: ''
})

// 搜索状态
const searchLoading = ref(false)
const hasSearched = ref(false)

// 地区选项
const regionOptions = [
  {
    value: 'beijing',
    label: '北京市',
    children: [
      { value: 'chaoyang', label: '朝阳区' },
      { value: 'haidian', label: '海淀区' },
      { value: 'dongcheng', label: '东城区' }
    ]
  },
  {
    value: 'shanghai',
    label: '上海市',
    children: [
      { value: 'huangpu', label: '黄浦区' },
      { value: 'xuhui', label: '徐汇区' },
      { value: 'changning', label: '长宁区' }
    ]
  }
]

// 搜索结果
const searchResults = ref([
  {
    id: '1',
    enterpriseName: '绿色科技有限公司',
    creditCode: '91110000123456789X',
    industry: '新能源',
    scale: '中型企业',
    registrationTime: '2024-01-15',
    status: '1',
    totalEmissions: 2456.78,
    yearEmissions: 1234.56,
    reductionRate: 15.2,
    lastUpdateTime: '2024-07-28'
  },
  {
    id: '2',
    enterpriseName: '环保能源股份有限公司',
    creditCode: '91110000987654321Y',
    industry: '环保',
    scale: '大型企业',
    registrationTime: '2024-02-20',
    status: '1',
    totalEmissions: 5678.90,
    yearEmissions: 2890.45,
    reductionRate: 12.8,
    lastUpdateTime: '2024-07-27'
  }
])

// 分页
const pagination = reactive({
  currentPage: 1,
  pageSize: 10,
  total: 2
})

// 状态相关方法
const getStatusType = (status: string) => {
  const statusMap = {
    '1': 'success',
    '2': 'warning',
    '3': 'danger'
  }
  return statusMap[status] || 'info'
}

const getStatusText = (status: string) => {
  const statusMap = {
    '1': '正常',
    '2': '冻结',
    '3': '注销'
  }
  return statusMap[status] || '未知'
}

// 事件处理方法
const handleSearch = async () => {
  searchLoading.value = true
  hasSearched.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 这里应该调用真实的搜索API
    ElMessage.success('搜索完成')
  } catch (error) {
    ElMessage.error('搜索失败')
  } finally {
    searchLoading.value = false
  }
}

const handleReset = () => {
  Object.assign(searchForm, {
    enterpriseName: '',
    creditCode: '',
    industry: '',
    scale: '',
    region: [],
    emissionRange: ''
  })
  searchResults.value = []
  hasSearched.value = false
}

const handleAdvancedSearch = () => {
  ElMessage.info('高级搜索功能待实现')
}

const handleExportResults = () => {
  ElMessage.info('导出结果功能待实现')
}

const handleBatchCompare = () => {
  ElMessage.info('批量对比功能待实现')
}

const handleViewDetail = (enterprise: any) => {
  router.push(`/carbon-account-admin/carbon-ledger/enterprise-info/${enterprise.id}`)
}

const handleViewReport = (enterprise: any) => {
  ElMessage.info('查看报告功能待实现')
}

const handleCompare = (enterprise: any) => {
  ElMessage.info('加入对比功能待实现')
}

const handleSizeChange = (size: number) => {
  pagination.pageSize = size
  handleSearch()
}

const handleCurrentChange = (page: number) => {
  pagination.currentPage = page
  handleSearch()
}
</script>

<style lang="less" scoped>
.carbon-ledger-search {
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: 600;
    color: #303133;
    
    .header-actions {
      display: flex;
      gap: 10px;
    }
  }
  
  .text-center {
    text-align: center;
  }
  
  .results-list {
    .result-item {
      border: 1px solid #ebeef5;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 15px;
      cursor: pointer;
      transition: all 0.3s;
      display: flex;
      justify-content: space-between;
      align-items: center;
      
      &:hover {
        border-color: #409eff;
        box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
      }
      
      .enterprise-info {
        flex: 1;
        
        .enterprise-header {
          display: flex;
          align-items: center;
          justify-content: space-between;
          margin-bottom: 15px;
          
          .enterprise-name {
            margin: 0;
            color: #303133;
            font-size: 18px;
          }
        }
        
        .enterprise-details {
          margin-bottom: 15px;
          
          .detail-item {
            margin-bottom: 8px;
            
            label {
              font-weight: 600;
              color: #606266;
              margin-right: 8px;
            }
            
            span {
              color: #303133;
            }
          }
        }
        
        .carbon-data {
          background: #f8f9fa;
          padding: 15px;
          border-radius: 6px;
          
          .carbon-item {
            text-align: center;
            
            .carbon-value {
              font-size: 20px;
              font-weight: bold;
              color: #409eff;
              margin-bottom: 5px;
            }
            
            .carbon-label {
              font-size: 12px;
              color: #909399;
            }
          }
        }
      }
      
      .enterprise-actions {
        display: flex;
        flex-direction: column;
        gap: 10px;
        margin-left: 20px;
      }
    }
  }
  
  .pagination-wrapper {
    margin-top: 20px;
    text-align: center;
  }
  
  .empty-state {
    text-align: center;
    padding: 60px 0;
  }
}
</style>
