import request from '@/config/axios'

// 企业账户列表
export interface EnterpriseAccountItem {
  id: string
  enterpriseName: string
  creditCode: string
  industry: string
  scale: string
  registrationTime: string
  status: string
  contactPerson: string
  contactPhone: string
  contactEmail: string
  registeredAddress: string
  legalPerson: string
  registeredCapital: string
  establishmentDate: string
  businessScope: string
  carbonScore: number
  totalEmissions: number
  lastLoginTime: string
}

// 获取企业账户列表
export const getEnterpriseAccountList = (params: any) => {
  return request.get({ url: '/carbon-account-admin/enterprise-account/list', params })
}

// 获取企业账户详情
export const getEnterpriseAccountDetail = (id: string) => {
  return request.get({ url: `/carbon-account-admin/enterprise-account/detail/${id}` })
}

// 冻结企业账户
export const freezeEnterpriseAccount = (id: string) => {
  return request.post({ url: `/carbon-account-admin/enterprise-account/freeze/${id}` })
}

// 解冻企业账户
export const unfreezeEnterpriseAccount = (id: string) => {
  return request.post({ url: `/carbon-account-admin/enterprise-account/unfreeze/${id}` })
}

// 注销企业账户
export const cancelEnterpriseAccount = (id: string) => {
  return request.post({ url: `/carbon-account-admin/enterprise-account/cancel/${id}` })
}

// 获取注册审核列表
export const getRegistrationAuditList = (params: any) => {
  return request.get({ url: '/carbon-account-admin/enterprise-account/registration-audit/list', params })
}

// 获取注册审核详情
export const getRegistrationAuditDetail = (id: string) => {
  return request.get({ url: `/carbon-account-admin/enterprise-account/registration-audit/detail/${id}` })
}

// 审核通过
export const approveRegistration = (id: string, data: any) => {
  return request.post({ url: `/carbon-account-admin/enterprise-account/registration-audit/approve/${id}`, data })
}

// 审核拒绝
export const rejectRegistration = (id: string, data: any) => {
  return request.post({ url: `/carbon-account-admin/enterprise-account/registration-audit/reject/${id}`, data })
}

// 获取用户详情
export const getUserDetail = (id: string) => {
  return request.get({ url: `/carbon-account-admin/enterprise-account/user/detail/${id}` })
}

// 获取角色列表
export const getRoleList = (params: any) => {
  return request.get({ url: '/carbon-account-admin/enterprise-account/role/list', params })
}

// 获取角色详情
export const getRoleDetail = (id: string) => {
  return request.get({ url: `/carbon-account-admin/enterprise-account/role/detail/${id}` })
}

// 创建角色
export const createRole = (data: any) => {
  return request.post({ url: '/carbon-account-admin/enterprise-account/role/create', data })
}

// 更新角色
export const updateRole = (id: string, data: any) => {
  return request.put({ url: `/carbon-account-admin/enterprise-account/role/update/${id}`, data })
}

// 删除角色
export const deleteRole = (ids: string[]) => {
  return request.delete({ url: '/carbon-account-admin/enterprise-account/role/delete', data: { ids } })
}

// 启用/禁用角色
export const toggleRoleStatus = (id: string, status: string) => {
  return request.post({ url: `/carbon-account-admin/enterprise-account/role/toggle-status/${id}`, data: { status } })
}

// 获取权限树
export const getPermissionTree = () => {
  return request.get({ url: '/carbon-account-admin/enterprise-account/permission/tree' })
}

// Mock数据生成器
export const mockEnterpriseAccountList = (params: any) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = {
        list: [
          {
            id: '1',
            enterpriseName: '绿色科技有限公司',
            creditCode: '91110000123456789X',
            industry: '新能源',
            scale: '中型企业',
            registrationTime: '2024-01-15',
            status: '1',
            contactPerson: '张三',
            contactPhone: '***********',
            contactEmail: '<EMAIL>',
            registeredAddress: '北京市朝阳区科技园区创新大厦A座1001室',
            legalPerson: '张三',
            registeredCapital: '1000万元',
            establishmentDate: '2020-05-15',
            businessScope: '新能源技术开发、技术咨询、技术服务',
            carbonScore: 8520,
            totalEmissions: 2456.78,
            lastLoginTime: '2024-07-28 16:45:00'
          },
          {
            id: '2',
            enterpriseName: '环保能源股份有限公司',
            creditCode: '91110000987654321Y',
            industry: '环保',
            scale: '大型企业',
            registrationTime: '2024-02-20',
            status: '1',
            contactPerson: '李四',
            contactPhone: '***********',
            contactEmail: '<EMAIL>',
            registeredAddress: '上海市浦东新区环保大厦B座2001室',
            legalPerson: '李四',
            registeredCapital: '5000万元',
            establishmentDate: '2018-03-20',
            businessScope: '环保设备制造、环保工程施工、环保技术咨询',
            carbonScore: 9200,
            totalEmissions: 5678.90,
            lastLoginTime: '2024-07-27 14:30:00'
          },
          {
            id: '3',
            enterpriseName: '智能制造有限公司',
            creditCode: '91110000456789123Z',
            industry: '制造业',
            scale: '中型企业',
            registrationTime: '2024-03-10',
            status: '2',
            contactPerson: '王五',
            contactPhone: '***********',
            contactEmail: '<EMAIL>',
            registeredAddress: '广州市天河区智能制造园C座3001室',
            legalPerson: '王五',
            registeredCapital: '2000万元',
            establishmentDate: '2019-08-10',
            businessScope: '智能设备制造、自动化系统集成、技术开发',
            carbonScore: 7800,
            totalEmissions: 3456.78,
            lastLoginTime: '2024-07-26 10:15:00'
          }
        ],
        total: 3
      }
      resolve(mockData)
    }, 500)
  })
}

export const mockRegistrationAuditList = (params: any) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = {
        list: [
          {
            id: '1',
            enterpriseName: '新能源科技有限公司',
            creditCode: '91110000111111111A',
            industry: '新能源',
            applicantName: '赵六',
            applicantPhone: '***********',
            applicationTime: '2024-07-25',
            status: '0',
            auditTime: '',
            auditor: '',
            auditReason: ''
          },
          {
            id: '2',
            enterpriseName: '清洁能源投资有限公司',
            creditCode: '91110000222222222B',
            industry: '投资',
            applicantName: '孙七',
            applicantPhone: '13800138005',
            applicationTime: '2024-07-24',
            status: '1',
            auditTime: '2024-07-26',
            auditor: '管理员',
            auditReason: '资料齐全，符合注册条件'
          },
          {
            id: '3',
            enterpriseName: '传统能源有限公司',
            creditCode: '91110000333333333C',
            industry: '传统能源',
            applicantName: '周八',
            applicantPhone: '13800138006',
            applicationTime: '2024-07-23',
            status: '2',
            auditTime: '2024-07-25',
            auditor: '管理员',
            auditReason: '不符合绿色发展要求'
          }
        ],
        total: 3,
        statistics: {
          pending: 1,
          approved: 1,
          rejected: 1
        }
      }
      resolve(mockData)
    }, 500)
  })
}

export const mockRoleList = (params: any) => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = {
        list: [
          {
            id: '1',
            roleName: '超级管理员',
            roleCode: 'super_admin',
            description: '系统超级管理员，拥有所有权限',
            permissions: [
              { id: '1', name: '用户管理' },
              { id: '2', name: '角色管理' },
              { id: '3', name: '权限管理' },
              { id: '4', name: '系统设置' }
            ],
            userCount: 1,
            createTime: '2024-01-01',
            status: '1',
            isSystem: true
          },
          {
            id: '2',
            roleName: '企业管理员',
            roleCode: 'enterprise_admin',
            description: '企业管理员，负责企业内部用户和数据管理',
            permissions: [
              { id: '1', name: '用户管理' },
              { id: '5', name: '数据管理' },
              { id: '6', name: '报表查看' }
            ],
            userCount: 5,
            createTime: '2024-01-15',
            status: '1',
            isSystem: false
          },
          {
            id: '3',
            roleName: '数据操作员',
            roleCode: 'data_operator',
            description: '数据操作员，负责数据录入和基础操作',
            permissions: [
              { id: '5', name: '数据管理' },
              { id: '7', name: '数据录入' }
            ],
            userCount: 12,
            createTime: '2024-02-01',
            status: '1',
            isSystem: false
          },
          {
            id: '4',
            roleName: '数据查看员',
            roleCode: 'data_viewer',
            description: '数据查看员，只能查看相关数据',
            permissions: [
              { id: '6', name: '报表查看' },
              { id: '8', name: '数据查看' }
            ],
            userCount: 8,
            createTime: '2024-02-15',
            status: '2',
            isSystem: false
          }
        ],
        total: 4
      }
      resolve(mockData)
    }, 500)
  })
}

export const mockPermissionTree = () => {
  return new Promise((resolve) => {
    setTimeout(() => {
      const mockData = [
        {
          id: '1',
          name: '系统管理',
          children: [
            { id: '11', name: '用户管理' },
            { id: '12', name: '角色管理' },
            { id: '13', name: '权限管理' },
            { id: '14', name: '系统设置' }
          ]
        },
        {
          id: '2',
          name: '企业管理',
          children: [
            { id: '21', name: '企业信息' },
            { id: '22', name: '企业用户' },
            { id: '23', name: '企业审核' }
          ]
        },
        {
          id: '3',
          name: '数据管理',
          children: [
            { id: '31', name: '数据查看' },
            { id: '32', name: '数据编辑' },
            { id: '33', name: '数据导出' },
            { id: '34', name: '数据导入' }
          ]
        },
        {
          id: '4',
          name: '报表管理',
          children: [
            { id: '41', name: '报表查看' },
            { id: '42', name: '报表生成' },
            { id: '43', name: '报表导出' }
          ]
        }
      ]
      resolve(mockData)
    }, 300)
  })
}
